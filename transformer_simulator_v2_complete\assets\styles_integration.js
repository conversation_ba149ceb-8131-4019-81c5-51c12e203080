/* 
 * styles_integration.js
 * Este script configura callbacks clientside para integração entre estilos JavaScript e Python.
 * O objetivo é permitir que os estilos definidos em JavaScript sejam facilmente acessíveis via Python.
 */

// Verificar se o ambiente Dash está disponível
if (window.dash_clientside) {
    // Namespace para nossas funções
    window.dash_clientside.styles_integration = {
        
        // Essa função retorna os estilos atuais para componentes Python
        getStyles: function() {
            // Esperar até que os estilos estejam carregados
            return new Promise((resolve) => {
                if (typeof window.COLORS !== 'undefined' &&
                    typeof window.TYPOGRAPHY !== 'undefined' &&
                    typeof window.COMPONENTS !== 'undefined') {
                    // Estilos já carregados, retornar imediatamente
                    resolve({
                        colors: window.COLORS,
                        typography: window.TYPOGRAPHY,
                        components: window.COMPONENTS,
                        tableStyles: window.TABLE_STYLES,
                        messageStyles: window.MESSAGE_STYLES,
                        spacing: window.SPACING
                    });
                } else {
                    // Aguardar o evento styles_loaded
                    document.addEventListener('styles_loaded', function() {
                        resolve({
                            colors: window.COLORS,
                            typography: window.TYPOGRAPHY,
                            components: window.COMPONENTS,
                            tableStyles: window.TABLE_STYLES,
                            messageStyles: window.MESSAGE_STYLES,
                            spacing: window.SPACING
                        });
                    }, { once: true });
                    
                    // Timeout de segurança após 2 segundos
                    setTimeout(() => {
                        console.warn('Timeout esperando por estilos, usando valores padrão');
                        resolve({
                            colors: window.COLORS || {},
                            typography: window.TYPOGRAPHY || {},
                            components: window.COMPONENTS || {},
                            tableStyles: window.TABLE_STYLES || {},
                            messageStyles: window.MESSAGE_STYLES || {},
                            spacing: window.SPACING || {}
                        });
                    }, 2000);
                }
            });
        },
        
        // Atualiza o tema atual (claro/escuro)
        setTheme: function(themeName) {
            if (typeof window.setTheme === 'function') {
                window.setTheme(themeName);
                return true;
            }
            return false;
        }
    };
    
    // Registro do carregamento
    console.log('Callbacks clientside para estilos registrados');
}
