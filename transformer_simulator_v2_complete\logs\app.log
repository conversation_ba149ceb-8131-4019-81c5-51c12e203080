2025-05-23 23:28:40 - app - ERROR - <PERSON>rro inesperado ao processar módulo de callback losses: unexpected indent (losses.py, line 484) [app.py:202]
Traceback (most recent call last):
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\app.py", line 197, in <module>
    __import__(module_path)
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\callbacks\losses.py", line 484
    try:
IndentationError: unexpected indent
2025-05-24 21:12:43 - app - ERROR - Erro ao registrar callbacks de short_circuit explicitamente: invalid syntax (calculations.py, line 723) [app.py:224]
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\app.py", line 215, in <module>
    module = __import__(module_path, fromlist=[reg_func_name])
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\callbacks\short_circuit.py", line 14, in <module>
    from app_core.calculations import calculate_impedance_variation, calculate_short_circuit_params
  File "C:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\app_core\calculations.py", line 723
    idx2 = first_cross_idx + 1            if idx1 < len(t_after_peak) and idx2 < len(t_after_peak):
                                                                                                  ^
SyntaxError: invalid syntax
2025-05-24 21:12:43 - app - ERROR - Erro ao registrar callbacks de applied_voltage explicitamente: invalid syntax (calculations.py, line 723) [app.py:224]
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\app.py", line 215, in <module>
    module = __import__(module_path, fromlist=[reg_func_name])
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\callbacks\applied_voltage.py", line 23, in <module>
    from app_core.calculations import calculate_capacitive_load  # Função de cálculo principal
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\app_core\calculations.py", line 723
    idx2 = first_cross_idx + 1            if idx1 < len(t_after_peak) and idx2 < len(t_after_peak):
                                                                                                  ^
SyntaxError: invalid syntax
2025-05-24 21:39:02 - __main__ - CRITICAL - ERRO CRÍTICO AO CRIAR LAYOUT [app.py:176]
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\app.py", line 169, in <module>
    app.layout = create_main_layout(
                 ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\layouts\main_layout.py", line 432, in create_main_layout
    global_stores = create_global_stores(app)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\components\global_stores.py", line 33, in create_global_stores
    if hasattr(app, "mcp") and state_manager is not None:
                               ^^^^^^^^^^^^^
NameError: name 'state_manager' is not defined
2025-05-24 21:39:02 - __main__ - CRITICAL - Erro inesperado durante a criação do layout: name 'state_manager' is not defined [app.py:177]
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\app.py", line 169, in <module>
    app.layout = create_main_layout(
                 ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\layouts\main_layout.py", line 432, in create_main_layout
    global_stores = create_global_stores(app)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\components\global_stores.py", line 33, in create_global_stores
    if hasattr(app, "mcp") and state_manager is not None:
                               ^^^^^^^^^^^^^
NameError: name 'state_manager' is not defined
2025-05-24 21:39:02 - __main__ - ERROR - Início do servidor abortado devido a falha na criação do layout ou registro de callbacks. [app.py:265]
2025-05-24 21:48:57 - utils.input_change_logger - WARNING - [INPUT CHANGE] Registro SIMPLIFICADO ativado - mostrando apenas alterações importantes [input_change_logger.py:151]
2025-05-24 21:48:57 - utils.input_change_logger - WARNING - [INPUT CHANGE] Registro SIMPLIFICADO ativado - mostrando apenas alterações importantes [input_change_logger.py:151]
2025-05-24 21:48:57 - app - ERROR - Erro ao importar módulo de startup: No module named 'app_core.startup' [app.py:246]
Traceback (most recent call last):
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\app.py", line 235, in <module>
    from app_core.startup import seed_mcp
ModuleNotFoundError: No module named 'app_core.startup'
2025-05-24 21:48:57 - app_core.transformer_mcp - WARNING - [MCP SET] Módulo utils.mcp_persistence não disponível para auto_propagate [transformer_mcp.py:210]
2025-05-24 21:49:10 - __main__ - CRITICAL - ERRO CRÍTICO AO CRIAR LAYOUT [app.py:229]
Traceback (most recent call last):
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\app.py", line 222, in <module>
    app.layout = create_main_layout(
                 ^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\layouts\main_layout.py", line 432, in create_main_layout
    global_stores = create_global_stores(app)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\components\global_stores.py", line 34, in create_global_stores
    mcp_data = app.state_manager.get_data("transformer-inputs-store")
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'StateManager' object has no attribute 'get_data'
2025-05-24 21:49:10 - __main__ - CRITICAL - Erro inesperado durante a criação do layout: 'StateManager' object has no attribute 'get_data' [app.py:230]
Traceback (most recent call last):
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\app.py", line 222, in <module>
    app.layout = create_main_layout(
                 ^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\layouts\main_layout.py", line 432, in create_main_layout
    global_stores = create_global_stores(app)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\components\global_stores.py", line 34, in create_global_stores
    mcp_data = app.state_manager.get_data("transformer-inputs-store")
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'StateManager' object has no attribute 'get_data'
2025-05-24 21:49:10 - __main__ - ERROR - Início do servidor abortado devido a falha na criação do layout ou registro de callbacks. [app.py:347]
2025-05-24 21:49:19 - __main__ - CRITICAL - ERRO CRÍTICO AO CRIAR LAYOUT [app.py:229]
Traceback (most recent call last):
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\app.py", line 222, in <module>
    app.layout = create_main_layout(
                 ^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\layouts\main_layout.py", line 432, in create_main_layout
    global_stores = create_global_stores(app)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\components\global_stores.py", line 34, in create_global_stores
    mcp_data = app.state_manager.get_data("transformer-inputs-store")
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'StateManager' object has no attribute 'get_data'
2025-05-24 21:49:19 - __main__ - CRITICAL - Erro inesperado durante a criação do layout: 'StateManager' object has no attribute 'get_data' [app.py:230]
Traceback (most recent call last):
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\app.py", line 222, in <module>
    app.layout = create_main_layout(
                 ^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\layouts\main_layout.py", line 432, in create_main_layout
    global_stores = create_global_stores(app)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\components\global_stores.py", line 34, in create_global_stores
    mcp_data = app.state_manager.get_data("transformer-inputs-store")
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'StateManager' object has no attribute 'get_data'
2025-05-24 21:49:19 - __main__ - ERROR - Início do servidor abortado devido a falha na criação do layout ou registro de callbacks. [app.py:347]
2025-05-24 21:49:35 - __main__ - CRITICAL - ERRO CRÍTICO AO CRIAR LAYOUT [app.py:229]
Traceback (most recent call last):
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\app.py", line 222, in <module>
    app.layout = create_main_layout(
                 ^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\layouts\main_layout.py", line 432, in create_main_layout
    global_stores = create_global_stores(app)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\components\global_stores.py", line 34, in create_global_stores
    mcp_data = app.state_manager.get_data("transformer-inputs-store")
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'StateManager' object has no attribute 'get_data'
2025-05-24 21:49:35 - __main__ - CRITICAL - Erro inesperado durante a criação do layout: 'StateManager' object has no attribute 'get_data' [app.py:230]
Traceback (most recent call last):
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\app.py", line 222, in <module>
    app.layout = create_main_layout(
                 ^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\layouts\main_layout.py", line 432, in create_main_layout
    global_stores = create_global_stores(app)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\components\global_stores.py", line 34, in create_global_stores
    mcp_data = app.state_manager.get_data("transformer-inputs-store")
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'StateManager' object has no attribute 'get_data'
2025-05-24 21:49:35 - __main__ - ERROR - Início do servidor abortado devido a falha na criação do layout ou registro de callbacks. [app.py:347]
2025-05-24 21:49:42 - __main__ - CRITICAL - ERRO CRÍTICO AO CRIAR LAYOUT [app.py:229]
Traceback (most recent call last):
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\app.py", line 222, in <module>
    app.layout = create_main_layout(
                 ^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\layouts\main_layout.py", line 432, in create_main_layout
    global_stores = create_global_stores(app)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\components\global_stores.py", line 34, in create_global_stores
    mcp_data = app.state_manager.get_data("transformer-inputs-store")
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'StateManager' object has no attribute 'get_data'
2025-05-24 21:49:42 - __main__ - CRITICAL - Erro inesperado durante a criação do layout: 'StateManager' object has no attribute 'get_data' [app.py:230]
Traceback (most recent call last):
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\app.py", line 222, in <module>
    app.layout = create_main_layout(
                 ^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\layouts\main_layout.py", line 432, in create_main_layout
    global_stores = create_global_stores(app)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\components\global_stores.py", line 34, in create_global_stores
    mcp_data = app.state_manager.get_data("transformer-inputs-store")
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'StateManager' object has no attribute 'get_data'
2025-05-24 21:49:42 - __main__ - ERROR - Início do servidor abortado devido a falha na criação do layout ou registro de callbacks. [app.py:347]
2025-05-24 21:49:43 - __main__ - CRITICAL - ERRO CRÍTICO AO CRIAR LAYOUT [app.py:229]
Traceback (most recent call last):
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\app.py", line 222, in <module>
    app.layout = create_main_layout(
                 ^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\layouts\main_layout.py", line 432, in create_main_layout
    global_stores = create_global_stores(app)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\components\global_stores.py", line 34, in create_global_stores
    mcp_data = app.state_manager.get_data("transformer-inputs-store")
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'StateManager' object has no attribute 'get_data'
2025-05-24 21:49:43 - __main__ - CRITICAL - Erro inesperado durante a criação do layout: 'StateManager' object has no attribute 'get_data' [app.py:230]
Traceback (most recent call last):
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\app.py", line 222, in <module>
    app.layout = create_main_layout(
                 ^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\layouts\main_layout.py", line 432, in create_main_layout
    global_stores = create_global_stores(app)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\components\global_stores.py", line 34, in create_global_stores
    mcp_data = app.state_manager.get_data("transformer-inputs-store")
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'StateManager' object has no attribute 'get_data'
2025-05-24 21:49:43 - __main__ - ERROR - Início do servidor abortado devido a falha na criação do layout ou registro de callbacks. [app.py:347]
2025-05-24 21:49:52 - __main__ - CRITICAL - ERRO CRÍTICO AO CRIAR LAYOUT [app.py:229]
Traceback (most recent call last):
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\app.py", line 222, in <module>
    app.layout = create_main_layout(
                 ^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\layouts\main_layout.py", line 432, in create_main_layout
    global_stores = create_global_stores(app)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\components\global_stores.py", line 34, in create_global_stores
    mcp_data = app.state_manager.get_data("transformer-inputs-store")
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'StateManager' object has no attribute 'get_data'
2025-05-24 21:49:52 - __main__ - CRITICAL - Erro inesperado durante a criação do layout: 'StateManager' object has no attribute 'get_data' [app.py:230]
Traceback (most recent call last):
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\app.py", line 222, in <module>
    app.layout = create_main_layout(
                 ^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\layouts\main_layout.py", line 432, in create_main_layout
    global_stores = create_global_stores(app)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\components\global_stores.py", line 34, in create_global_stores
    mcp_data = app.state_manager.get_data("transformer-inputs-store")
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'StateManager' object has no attribute 'get_data'
2025-05-24 21:49:52 - __main__ - ERROR - Início do servidor abortado devido a falha na criação do layout ou registro de callbacks. [app.py:347]
2025-05-24 21:53:43 - __main__ - ERROR - Erro ao inicializar StateManager: cannot import name 'StateManager' from 'app_core.state_manager' (c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\app_core\state_manager.py) [app.py:150]
Traceback (most recent call last):
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\app.py", line 144, in <module>
    from app_core.state_manager import StateManager
ImportError: cannot import name 'StateManager' from 'app_core.state_manager' (c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\app_core\state_manager.py). Did you mean: 'state_manager'?
2025-05-24 21:53:43 - app - ERROR - Erro ao inicializar StateManager: cannot import name 'StateManager' from 'app_core.state_manager' (c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\app_core\state_manager.py) [app.py:150]
Traceback (most recent call last):
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\app.py", line 144, in <module>
    from app_core.state_manager import StateManager
ImportError: cannot import name 'StateManager' from 'app_core.state_manager' (c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\app_core\state_manager.py). Did you mean: 'state_manager'?
2025-05-24 22:00:17 - __main__ - CRITICAL - ERRO CRÍTICO AO CRIAR LAYOUT [app.py:177]
Traceback (most recent call last):
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\app.py", line 170, in <module>
    app.layout = create_main_layout(
                 ^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\layouts\main_layout.py", line 433, in create_main_layout
    global_components = [
                        ^
TypeError: Value after * must be an iterable, not NoneType
2025-05-24 22:00:17 - __main__ - CRITICAL - Erro inesperado durante a criação do layout: Value after * must be an iterable, not NoneType [app.py:178]
Traceback (most recent call last):
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\app.py", line 170, in <module>
    app.layout = create_main_layout(
                 ^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\layouts\main_layout.py", line 433, in create_main_layout
    global_components = [
                        ^
TypeError: Value after * must be an iterable, not NoneType
2025-05-24 22:00:17 - __main__ - ERROR - Início do servidor abortado devido a falha na criação do layout ou registro de callbacks. [app.py:266]
2025-05-24 22:00:27 - __main__ - CRITICAL - ERRO CRÍTICO AO CRIAR LAYOUT [app.py:177]
Traceback (most recent call last):
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\app.py", line 170, in <module>
    app.layout = create_main_layout(
                 ^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\layouts\main_layout.py", line 433, in create_main_layout
    global_components = [
                        ^
TypeError: Value after * must be an iterable, not NoneType
2025-05-24 22:00:27 - __main__ - CRITICAL - Erro inesperado durante a criação do layout: Value after * must be an iterable, not NoneType [app.py:178]
Traceback (most recent call last):
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\app.py", line 170, in <module>
    app.layout = create_main_layout(
                 ^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\layouts\main_layout.py", line 433, in create_main_layout
    global_components = [
                        ^
TypeError: Value after * must be an iterable, not NoneType
2025-05-24 22:00:27 - __main__ - ERROR - Início do servidor abortado devido a falha na criação do layout ou registro de callbacks. [app.py:266]
2025-05-25 00:06:05 - __main__ - CRITICAL - ERRO CRÍTICO AO CRIAR LAYOUT [app.py:177]
Traceback (most recent call last):
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\app.py", line 170, in <module>
    app.layout = create_main_layout(
                 ^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\layouts\main_layout.py", line 433, in create_main_layout
    global_components = [
                        ^
TypeError: Value after * must be an iterable, not NoneType
2025-05-25 00:06:05 - __main__ - CRITICAL - Erro inesperado durante a criação do layout: Value after * must be an iterable, not NoneType [app.py:178]
Traceback (most recent call last):
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\app.py", line 170, in <module>
    app.layout = create_main_layout(
                 ^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\layouts\main_layout.py", line 433, in create_main_layout
    global_components = [
                        ^
TypeError: Value after * must be an iterable, not NoneType
2025-05-25 00:06:05 - __main__ - ERROR - Início do servidor abortado devido a falha na criação do layout ou registro de callbacks. [app.py:266]
2025-05-25 00:06:11 - __main__ - CRITICAL - ERRO CRÍTICO AO CRIAR LAYOUT [app.py:177]
Traceback (most recent call last):
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\app.py", line 170, in <module>
    app.layout = create_main_layout(
                 ^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\layouts\main_layout.py", line 433, in create_main_layout
    global_components = [
                        ^
TypeError: Value after * must be an iterable, not NoneType
2025-05-25 00:06:11 - __main__ - CRITICAL - Erro inesperado durante a criação do layout: Value after * must be an iterable, not NoneType [app.py:178]
Traceback (most recent call last):
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\app.py", line 170, in <module>
    app.layout = create_main_layout(
                 ^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\layouts\main_layout.py", line 433, in create_main_layout
    global_components = [
                        ^
TypeError: Value after * must be an iterable, not NoneType
2025-05-25 00:06:11 - __main__ - ERROR - Início do servidor abortado devido a falha na criação do layout ou registro de callbacks. [app.py:266]
2025-05-25 13:30:09 - __main__ - CRITICAL - ERRO CRÍTICO AO CRIAR LAYOUT [app.py:177]
Traceback (most recent call last):
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\app.py", line 168, in <module>
    from layouts.main_layout import create_main_layout
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\layouts\main_layout.py", line 9, in <module>
    from layouts import COLORS, COMPONENTS, SPACING, TYPOGRAPHY
ImportError: cannot import name 'COLORS' from 'layouts' (c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\layouts\__init__.py)
2025-05-25 13:30:09 - __main__ - CRITICAL - Erro inesperado durante a criação do layout: cannot import name 'COLORS' from 'layouts' (c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\layouts\__init__.py) [app.py:178]
Traceback (most recent call last):
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\app.py", line 168, in <module>
    from layouts.main_layout import create_main_layout
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\layouts\main_layout.py", line 9, in <module>
    from layouts import COLORS, COMPONENTS, SPACING, TYPOGRAPHY
ImportError: cannot import name 'COLORS' from 'layouts' (c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\layouts\__init__.py)
2025-05-25 13:30:09 - __main__ - ERROR - Início do servidor abortado devido a falha na criação do layout ou registro de callbacks. [app.py:266]
2025-05-25 13:30:12 - __main__ - CRITICAL - ERRO CRÍTICO AO CRIAR LAYOUT [app.py:177]
Traceback (most recent call last):
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\app.py", line 168, in <module>
    from layouts.main_layout import create_main_layout
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\layouts\main_layout.py", line 9, in <module>
    from layouts import COLORS, COMPONENTS, SPACING, TYPOGRAPHY
ImportError: cannot import name 'COLORS' from 'layouts' (c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\layouts\__init__.py)
2025-05-25 13:30:12 - __main__ - CRITICAL - Erro inesperado durante a criação do layout: cannot import name 'COLORS' from 'layouts' (c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\layouts\__init__.py) [app.py:178]
Traceback (most recent call last):
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\app.py", line 168, in <module>
    from layouts.main_layout import create_main_layout
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\layouts\main_layout.py", line 9, in <module>
    from layouts import COLORS, COMPONENTS, SPACING, TYPOGRAPHY
ImportError: cannot import name 'COLORS' from 'layouts' (c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\layouts\__init__.py)
2025-05-25 13:30:12 - __main__ - ERROR - Início do servidor abortado devido a falha na criação do layout ou registro de callbacks. [app.py:266]
2025-05-25 13:30:14 - __main__ - CRITICAL - ERRO CRÍTICO AO CRIAR LAYOUT [app.py:177]
Traceback (most recent call last):
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\app.py", line 168, in <module>
    from layouts.main_layout import create_main_layout
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\layouts\main_layout.py", line 9, in <module>
    from layouts import COLORS, COMPONENTS, SPACING, TYPOGRAPHY
ImportError: cannot import name 'COLORS' from 'layouts' (c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\layouts\__init__.py)
2025-05-25 13:30:14 - __main__ - CRITICAL - Erro inesperado durante a criação do layout: cannot import name 'COLORS' from 'layouts' (c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\layouts\__init__.py) [app.py:178]
Traceback (most recent call last):
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\app.py", line 168, in <module>
    from layouts.main_layout import create_main_layout
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\layouts\main_layout.py", line 9, in <module>
    from layouts import COLORS, COMPONENTS, SPACING, TYPOGRAPHY
ImportError: cannot import name 'COLORS' from 'layouts' (c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\layouts\__init__.py)
2025-05-25 13:30:14 - __main__ - ERROR - Início do servidor abortado devido a falha na criação do layout ou registro de callbacks. [app.py:266]
2025-05-25 13:30:26 - __main__ - CRITICAL - ERRO CRÍTICO AO CRIAR LAYOUT [app.py:177]
Traceback (most recent call last):
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\app.py", line 168, in <module>
    from layouts.main_layout import create_main_layout
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\layouts\main_layout.py", line 9, in <module>
    from layouts import COLORS, COMPONENTS, SPACING, TYPOGRAPHY
ImportError: cannot import name 'COLORS' from 'layouts' (c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\layouts\__init__.py)
2025-05-25 13:30:26 - __main__ - CRITICAL - Erro inesperado durante a criação do layout: cannot import name 'COLORS' from 'layouts' (c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\layouts\__init__.py) [app.py:178]
Traceback (most recent call last):
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\app.py", line 168, in <module>
    from layouts.main_layout import create_main_layout
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\layouts\main_layout.py", line 9, in <module>
    from layouts import COLORS, COMPONENTS, SPACING, TYPOGRAPHY
ImportError: cannot import name 'COLORS' from 'layouts' (c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\layouts\__init__.py)
2025-05-25 13:30:26 - __main__ - ERROR - Início do servidor abortado devido a falha na criação do layout ou registro de callbacks. [app.py:266]
2025-05-25 13:30:29 - __main__ - CRITICAL - ERRO CRÍTICO AO CRIAR LAYOUT [app.py:177]
Traceback (most recent call last):
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\app.py", line 168, in <module>
    from layouts.main_layout import create_main_layout
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\layouts\main_layout.py", line 9, in <module>
    from layouts import COLORS, COMPONENTS, SPACING, TYPOGRAPHY
ImportError: cannot import name 'COLORS' from 'layouts' (c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\layouts\__init__.py)
2025-05-25 13:30:29 - __main__ - CRITICAL - Erro inesperado durante a criação do layout: cannot import name 'COLORS' from 'layouts' (c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\layouts\__init__.py) [app.py:178]
Traceback (most recent call last):
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\app.py", line 168, in <module>
    from layouts.main_layout import create_main_layout
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\layouts\main_layout.py", line 9, in <module>
    from layouts import COLORS, COMPONENTS, SPACING, TYPOGRAPHY
ImportError: cannot import name 'COLORS' from 'layouts' (c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\layouts\__init__.py)
2025-05-25 13:30:29 - __main__ - ERROR - Início do servidor abortado devido a falha na criação do layout ou registro de callbacks. [app.py:266]
2025-05-25 13:30:33 - __main__ - CRITICAL - ERRO CRÍTICO AO CRIAR LAYOUT [app.py:177]
Traceback (most recent call last):
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\app.py", line 168, in <module>
    from layouts.main_layout import create_main_layout
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\layouts\main_layout.py", line 9, in <module>
    from layouts import COLORS, COMPONENTS, SPACING, TYPOGRAPHY
ImportError: cannot import name 'COLORS' from 'layouts' (c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\layouts\__init__.py)
2025-05-25 13:30:33 - __main__ - CRITICAL - Erro inesperado durante a criação do layout: cannot import name 'COLORS' from 'layouts' (c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\layouts\__init__.py) [app.py:178]
Traceback (most recent call last):
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\app.py", line 168, in <module>
    from layouts.main_layout import create_main_layout
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\layouts\main_layout.py", line 9, in <module>
    from layouts import COLORS, COMPONENTS, SPACING, TYPOGRAPHY
ImportError: cannot import name 'COLORS' from 'layouts' (c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\layouts\__init__.py)
2025-05-25 13:30:33 - __main__ - ERROR - Início do servidor abortado devido a falha na criação do layout ou registro de callbacks. [app.py:266]
2025-05-25 13:30:42 - __main__ - CRITICAL - ERRO CRÍTICO AO CRIAR LAYOUT [app.py:177]
Traceback (most recent call last):
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\app.py", line 168, in <module>
    from layouts.main_layout import create_main_layout
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\layouts\main_layout.py", line 9, in <module>
    from layouts import COLORS, COMPONENTS, SPACING, TYPOGRAPHY
ImportError: cannot import name 'COLORS' from 'layouts' (c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\layouts\__init__.py)
2025-05-25 13:30:42 - __main__ - CRITICAL - Erro inesperado durante a criação do layout: cannot import name 'COLORS' from 'layouts' (c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\layouts\__init__.py) [app.py:178]
Traceback (most recent call last):
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\app.py", line 168, in <module>
    from layouts.main_layout import create_main_layout
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\layouts\main_layout.py", line 9, in <module>
    from layouts import COLORS, COMPONENTS, SPACING, TYPOGRAPHY
ImportError: cannot import name 'COLORS' from 'layouts' (c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\layouts\__init__.py)
2025-05-25 13:30:42 - __main__ - ERROR - Início do servidor abortado devido a falha na criação do layout ou registro de callbacks. [app.py:266]
2025-05-25 13:30:44 - __main__ - CRITICAL - ERRO CRÍTICO AO CRIAR LAYOUT [app.py:177]
Traceback (most recent call last):
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\app.py", line 168, in <module>
    from layouts.main_layout import create_main_layout
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\layouts\main_layout.py", line 9, in <module>
    from layouts import COLORS, COMPONENTS, SPACING, TYPOGRAPHY
ImportError: cannot import name 'COLORS' from 'layouts' (c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\layouts\__init__.py)
2025-05-25 13:30:44 - __main__ - CRITICAL - Erro inesperado durante a criação do layout: cannot import name 'COLORS' from 'layouts' (c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\layouts\__init__.py) [app.py:178]
Traceback (most recent call last):
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\app.py", line 168, in <module>
    from layouts.main_layout import create_main_layout
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\layouts\main_layout.py", line 9, in <module>
    from layouts import COLORS, COMPONENTS, SPACING, TYPOGRAPHY
ImportError: cannot import name 'COLORS' from 'layouts' (c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\layouts\__init__.py)
2025-05-25 13:30:44 - __main__ - ERROR - Início do servidor abortado devido a falha na criação do layout ou registro de callbacks. [app.py:266]
2025-05-25 13:30:52 - __main__ - CRITICAL - ERRO CRÍTICO AO CRIAR LAYOUT [app.py:177]
Traceback (most recent call last):
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\app.py", line 168, in <module>
    from layouts.main_layout import create_main_layout
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\layouts\main_layout.py", line 9, in <module>
    from layouts import COLORS, COMPONENTS, SPACING, TYPOGRAPHY
ImportError: cannot import name 'COLORS' from 'layouts' (c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\layouts\__init__.py)
2025-05-25 13:30:52 - __main__ - CRITICAL - Erro inesperado durante a criação do layout: cannot import name 'COLORS' from 'layouts' (c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\layouts\__init__.py) [app.py:178]
Traceback (most recent call last):
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\app.py", line 168, in <module>
    from layouts.main_layout import create_main_layout
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\layouts\main_layout.py", line 9, in <module>
    from layouts import COLORS, COMPONENTS, SPACING, TYPOGRAPHY
ImportError: cannot import name 'COLORS' from 'layouts' (c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\layouts\__init__.py)
2025-05-25 13:30:52 - __main__ - ERROR - Início do servidor abortado devido a falha na criação do layout ou registro de callbacks. [app.py:266]
2025-05-25 13:30:53 - __main__ - CRITICAL - ERRO CRÍTICO AO CRIAR LAYOUT [app.py:177]
Traceback (most recent call last):
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\app.py", line 168, in <module>
    from layouts.main_layout import create_main_layout
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\layouts\main_layout.py", line 9, in <module>
    from layouts import COLORS, COMPONENTS, SPACING, TYPOGRAPHY
ImportError: cannot import name 'COLORS' from 'layouts' (c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\layouts\__init__.py)
2025-05-25 13:30:53 - __main__ - CRITICAL - Erro inesperado durante a criação do layout: cannot import name 'COLORS' from 'layouts' (c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\layouts\__init__.py) [app.py:178]
Traceback (most recent call last):
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\app.py", line 168, in <module>
    from layouts.main_layout import create_main_layout
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\layouts\main_layout.py", line 9, in <module>
    from layouts import COLORS, COMPONENTS, SPACING, TYPOGRAPHY
ImportError: cannot import name 'COLORS' from 'layouts' (c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\layouts\__init__.py)
2025-05-25 13:30:53 - __main__ - ERROR - Início do servidor abortado devido a falha na criação do layout ou registro de callbacks. [app.py:266]
2025-05-25 13:30:57 - __main__ - CRITICAL - ERRO CRÍTICO AO CRIAR LAYOUT [app.py:177]
Traceback (most recent call last):
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\app.py", line 168, in <module>
    from layouts.main_layout import create_main_layout
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\layouts\main_layout.py", line 9, in <module>
    from layouts import COLORS, COMPONENTS, SPACING, TYPOGRAPHY
ImportError: cannot import name 'COLORS' from 'layouts' (c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\layouts\__init__.py)
2025-05-25 13:30:57 - __main__ - CRITICAL - Erro inesperado durante a criação do layout: cannot import name 'COLORS' from 'layouts' (c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\layouts\__init__.py) [app.py:178]
Traceback (most recent call last):
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\app.py", line 168, in <module>
    from layouts.main_layout import create_main_layout
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\layouts\main_layout.py", line 9, in <module>
    from layouts import COLORS, COMPONENTS, SPACING, TYPOGRAPHY
ImportError: cannot import name 'COLORS' from 'layouts' (c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\layouts\__init__.py)
2025-05-25 13:30:57 - __main__ - ERROR - Início do servidor abortado devido a falha na criação do layout ou registro de callbacks. [app.py:266]
2025-05-25 13:30:59 - __main__ - CRITICAL - ERRO CRÍTICO AO CRIAR LAYOUT [app.py:177]
Traceback (most recent call last):
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\app.py", line 168, in <module>
    from layouts.main_layout import create_main_layout
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\layouts\main_layout.py", line 9, in <module>
    from layouts import COLORS, COMPONENTS, SPACING, TYPOGRAPHY
ImportError: cannot import name 'COLORS' from 'layouts' (c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\layouts\__init__.py)
2025-05-25 13:30:59 - __main__ - CRITICAL - Erro inesperado durante a criação do layout: cannot import name 'COLORS' from 'layouts' (c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\layouts\__init__.py) [app.py:178]
Traceback (most recent call last):
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\app.py", line 168, in <module>
    from layouts.main_layout import create_main_layout
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\layouts\main_layout.py", line 9, in <module>
    from layouts import COLORS, COMPONENTS, SPACING, TYPOGRAPHY
ImportError: cannot import name 'COLORS' from 'layouts' (c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\layouts\__init__.py)
2025-05-25 13:30:59 - __main__ - ERROR - Início do servidor abortado devido a falha na criação do layout ou registro de callbacks. [app.py:266]
2025-05-25 13:31:01 - __main__ - CRITICAL - ERRO CRÍTICO AO CRIAR LAYOUT [app.py:177]
Traceback (most recent call last):
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\app.py", line 168, in <module>
    from layouts.main_layout import create_main_layout
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\layouts\main_layout.py", line 9, in <module>
    from layouts import COLORS, COMPONENTS, SPACING, TYPOGRAPHY
ImportError: cannot import name 'COLORS' from 'layouts' (c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\layouts\__init__.py)
2025-05-25 13:31:01 - __main__ - CRITICAL - Erro inesperado durante a criação do layout: cannot import name 'COLORS' from 'layouts' (c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\layouts\__init__.py) [app.py:178]
Traceback (most recent call last):
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\app.py", line 168, in <module>
    from layouts.main_layout import create_main_layout
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\layouts\main_layout.py", line 9, in <module>
    from layouts import COLORS, COMPONENTS, SPACING, TYPOGRAPHY
ImportError: cannot import name 'COLORS' from 'layouts' (c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\layouts\__init__.py)
2025-05-25 13:31:01 - __main__ - ERROR - Início do servidor abortado devido a falha na criação do layout ou registro de callbacks. [app.py:266]
2025-05-25 13:31:03 - __main__ - CRITICAL - ERRO CRÍTICO AO CRIAR LAYOUT [app.py:177]
Traceback (most recent call last):
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\app.py", line 168, in <module>
    from layouts.main_layout import create_main_layout
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\layouts\main_layout.py", line 9, in <module>
    from layouts import COLORS, COMPONENTS, SPACING, TYPOGRAPHY
ImportError: cannot import name 'COLORS' from 'layouts' (c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\layouts\__init__.py)
2025-05-25 13:31:03 - __main__ - CRITICAL - Erro inesperado durante a criação do layout: cannot import name 'COLORS' from 'layouts' (c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\layouts\__init__.py) [app.py:178]
Traceback (most recent call last):
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\app.py", line 168, in <module>
    from layouts.main_layout import create_main_layout
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\layouts\main_layout.py", line 9, in <module>
    from layouts import COLORS, COMPONENTS, SPACING, TYPOGRAPHY
ImportError: cannot import name 'COLORS' from 'layouts' (c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\layouts\__init__.py)
2025-05-25 13:31:03 - __main__ - ERROR - Início do servidor abortado devido a falha na criação do layout ou registro de callbacks. [app.py:266]
2025-05-25 13:31:06 - __main__ - CRITICAL - ERRO CRÍTICO AO CRIAR LAYOUT [app.py:177]
Traceback (most recent call last):
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\app.py", line 168, in <module>
    from layouts.main_layout import create_main_layout
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\layouts\main_layout.py", line 9, in <module>
    from layouts import COLORS, COMPONENTS, SPACING, TYPOGRAPHY
ImportError: cannot import name 'COLORS' from 'layouts' (c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\layouts\__init__.py)
2025-05-25 13:31:06 - __main__ - CRITICAL - Erro inesperado durante a criação do layout: cannot import name 'COLORS' from 'layouts' (c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\layouts\__init__.py) [app.py:178]
Traceback (most recent call last):
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\app.py", line 168, in <module>
    from layouts.main_layout import create_main_layout
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\layouts\main_layout.py", line 9, in <module>
    from layouts import COLORS, COMPONENTS, SPACING, TYPOGRAPHY
ImportError: cannot import name 'COLORS' from 'layouts' (c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\layouts\__init__.py)
2025-05-25 13:31:06 - __main__ - ERROR - Início do servidor abortado devido a falha na criação do layout ou registro de callbacks. [app.py:266]
2025-05-25 13:31:07 - __main__ - CRITICAL - ERRO CRÍTICO AO CRIAR LAYOUT [app.py:177]
Traceback (most recent call last):
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\app.py", line 168, in <module>
    from layouts.main_layout import create_main_layout
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\layouts\main_layout.py", line 9, in <module>
    from layouts import COLORS, COMPONENTS, SPACING, TYPOGRAPHY
ImportError: cannot import name 'COLORS' from 'layouts' (c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\layouts\__init__.py)
2025-05-25 13:31:07 - __main__ - CRITICAL - Erro inesperado durante a criação do layout: cannot import name 'COLORS' from 'layouts' (c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\layouts\__init__.py) [app.py:178]
Traceback (most recent call last):
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\app.py", line 168, in <module>
    from layouts.main_layout import create_main_layout
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\layouts\main_layout.py", line 9, in <module>
    from layouts import COLORS, COMPONENTS, SPACING, TYPOGRAPHY
ImportError: cannot import name 'COLORS' from 'layouts' (c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\layouts\__init__.py)
2025-05-25 13:31:07 - __main__ - ERROR - Início do servidor abortado devido a falha na criação do layout ou registro de callbacks. [app.py:266]
2025-05-25 13:31:09 - __main__ - CRITICAL - ERRO CRÍTICO AO CRIAR LAYOUT [app.py:177]
Traceback (most recent call last):
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\app.py", line 168, in <module>
    from layouts.main_layout import create_main_layout
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\layouts\main_layout.py", line 9, in <module>
    from layouts import COLORS, COMPONENTS, SPACING, TYPOGRAPHY
ImportError: cannot import name 'COLORS' from 'layouts' (c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\layouts\__init__.py)
2025-05-25 13:31:09 - __main__ - CRITICAL - Erro inesperado durante a criação do layout: cannot import name 'COLORS' from 'layouts' (c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\layouts\__init__.py) [app.py:178]
Traceback (most recent call last):
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\app.py", line 168, in <module>
    from layouts.main_layout import create_main_layout
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\layouts\main_layout.py", line 9, in <module>
    from layouts import COLORS, COMPONENTS, SPACING, TYPOGRAPHY
ImportError: cannot import name 'COLORS' from 'layouts' (c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\layouts\__init__.py)
2025-05-25 13:31:09 - __main__ - ERROR - Início do servidor abortado devido a falha na criação do layout ou registro de callbacks. [app.py:266]
2025-05-25 13:31:11 - __main__ - CRITICAL - ERRO CRÍTICO AO CRIAR LAYOUT [app.py:177]
Traceback (most recent call last):
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\app.py", line 168, in <module>
    from layouts.main_layout import create_main_layout
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\layouts\main_layout.py", line 9, in <module>
    from layouts import COLORS, COMPONENTS, SPACING, TYPOGRAPHY
ImportError: cannot import name 'COLORS' from 'layouts' (c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\layouts\__init__.py)
2025-05-25 13:31:11 - __main__ - CRITICAL - Erro inesperado durante a criação do layout: cannot import name 'COLORS' from 'layouts' (c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\layouts\__init__.py) [app.py:178]
Traceback (most recent call last):
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\app.py", line 168, in <module>
    from layouts.main_layout import create_main_layout
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\layouts\main_layout.py", line 9, in <module>
    from layouts import COLORS, COMPONENTS, SPACING, TYPOGRAPHY
ImportError: cannot import name 'COLORS' from 'layouts' (c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\layouts\__init__.py)
2025-05-25 13:31:11 - __main__ - ERROR - Início do servidor abortado devido a falha na criação do layout ou registro de callbacks. [app.py:266]
2025-05-25 13:31:14 - __main__ - CRITICAL - ERRO CRÍTICO AO CRIAR LAYOUT [app.py:177]
Traceback (most recent call last):
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\app.py", line 168, in <module>
    from layouts.main_layout import create_main_layout
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\layouts\main_layout.py", line 9, in <module>
    from layouts import COLORS, COMPONENTS, SPACING, TYPOGRAPHY
ImportError: cannot import name 'COLORS' from 'layouts' (c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\layouts\__init__.py)
2025-05-25 13:31:14 - __main__ - CRITICAL - Erro inesperado durante a criação do layout: cannot import name 'COLORS' from 'layouts' (c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\layouts\__init__.py) [app.py:178]
Traceback (most recent call last):
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\app.py", line 168, in <module>
    from layouts.main_layout import create_main_layout
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\layouts\main_layout.py", line 9, in <module>
    from layouts import COLORS, COMPONENTS, SPACING, TYPOGRAPHY
ImportError: cannot import name 'COLORS' from 'layouts' (c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\layouts\__init__.py)
2025-05-25 13:31:14 - __main__ - ERROR - Início do servidor abortado devido a falha na criação do layout ou registro de callbacks. [app.py:266]
2025-05-25 13:31:16 - __main__ - CRITICAL - ERRO CRÍTICO AO CRIAR LAYOUT [app.py:177]
Traceback (most recent call last):
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\app.py", line 168, in <module>
    from layouts.main_layout import create_main_layout
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\layouts\main_layout.py", line 9, in <module>
    from layouts import COLORS, COMPONENTS, SPACING, TYPOGRAPHY
ImportError: cannot import name 'COLORS' from 'layouts' (c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\layouts\__init__.py)
2025-05-25 13:31:16 - __main__ - CRITICAL - Erro inesperado durante a criação do layout: cannot import name 'COLORS' from 'layouts' (c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\layouts\__init__.py) [app.py:178]
Traceback (most recent call last):
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\app.py", line 168, in <module>
    from layouts.main_layout import create_main_layout
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\layouts\main_layout.py", line 9, in <module>
    from layouts import COLORS, COMPONENTS, SPACING, TYPOGRAPHY
ImportError: cannot import name 'COLORS' from 'layouts' (c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\layouts\__init__.py)
2025-05-25 13:31:16 - __main__ - ERROR - Início do servidor abortado devido a falha na criação do layout ou registro de callbacks. [app.py:266]
2025-05-25 13:31:18 - __main__ - CRITICAL - ERRO CRÍTICO AO CRIAR LAYOUT [app.py:177]
Traceback (most recent call last):
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\app.py", line 168, in <module>
    from layouts.main_layout import create_main_layout
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\layouts\main_layout.py", line 9, in <module>
    from layouts import COLORS, COMPONENTS, SPACING, TYPOGRAPHY
ImportError: cannot import name 'COLORS' from 'layouts' (c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\layouts\__init__.py)
2025-05-25 13:31:18 - __main__ - CRITICAL - Erro inesperado durante a criação do layout: cannot import name 'COLORS' from 'layouts' (c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\layouts\__init__.py) [app.py:178]
Traceback (most recent call last):
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\app.py", line 168, in <module>
    from layouts.main_layout import create_main_layout
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\layouts\main_layout.py", line 9, in <module>
    from layouts import COLORS, COMPONENTS, SPACING, TYPOGRAPHY
ImportError: cannot import name 'COLORS' from 'layouts' (c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\layouts\__init__.py)
2025-05-25 13:31:18 - __main__ - ERROR - Início do servidor abortado devido a falha na criação do layout ou registro de callbacks. [app.py:266]
2025-05-25 13:31:19 - __main__ - CRITICAL - ERRO CRÍTICO AO CRIAR LAYOUT [app.py:177]
Traceback (most recent call last):
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\app.py", line 168, in <module>
    from layouts.main_layout import create_main_layout
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\layouts\main_layout.py", line 9, in <module>
    from layouts import COLORS, COMPONENTS, SPACING, TYPOGRAPHY
ImportError: cannot import name 'COLORS' from 'layouts' (c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\layouts\__init__.py)
2025-05-25 13:31:19 - __main__ - CRITICAL - Erro inesperado durante a criação do layout: cannot import name 'COLORS' from 'layouts' (c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\layouts\__init__.py) [app.py:178]
Traceback (most recent call last):
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\app.py", line 168, in <module>
    from layouts.main_layout import create_main_layout
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\layouts\main_layout.py", line 9, in <module>
    from layouts import COLORS, COMPONENTS, SPACING, TYPOGRAPHY
ImportError: cannot import name 'COLORS' from 'layouts' (c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\layouts\__init__.py)
2025-05-25 13:31:19 - __main__ - ERROR - Início do servidor abortado devido a falha na criação do layout ou registro de callbacks. [app.py:266]
2025-05-25 13:31:21 - __main__ - CRITICAL - ERRO CRÍTICO AO CRIAR LAYOUT [app.py:177]
Traceback (most recent call last):
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\app.py", line 168, in <module>
    from layouts.main_layout import create_main_layout
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\layouts\main_layout.py", line 9, in <module>
    from layouts import COLORS, COMPONENTS, SPACING, TYPOGRAPHY
ImportError: cannot import name 'COLORS' from 'layouts' (c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\layouts\__init__.py)
2025-05-25 13:31:21 - __main__ - CRITICAL - Erro inesperado durante a criação do layout: cannot import name 'COLORS' from 'layouts' (c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\layouts\__init__.py) [app.py:178]
Traceback (most recent call last):
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\app.py", line 168, in <module>
    from layouts.main_layout import create_main_layout
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\layouts\main_layout.py", line 9, in <module>
    from layouts import COLORS, COMPONENTS, SPACING, TYPOGRAPHY
ImportError: cannot import name 'COLORS' from 'layouts' (c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\layouts\__init__.py)
2025-05-25 13:31:21 - __main__ - ERROR - Início do servidor abortado devido a falha na criação do layout ou registro de callbacks. [app.py:266]
2025-05-25 13:31:25 - __main__ - CRITICAL - ERRO CRÍTICO AO CRIAR LAYOUT [app.py:177]
Traceback (most recent call last):
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\app.py", line 168, in <module>
    from layouts.main_layout import create_main_layout
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\layouts\main_layout.py", line 9, in <module>
    from layouts import COLORS, COMPONENTS, SPACING, TYPOGRAPHY
ImportError: cannot import name 'COLORS' from 'layouts' (c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\layouts\__init__.py)
2025-05-25 13:31:25 - __main__ - CRITICAL - Erro inesperado durante a criação do layout: cannot import name 'COLORS' from 'layouts' (c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\layouts\__init__.py) [app.py:178]
Traceback (most recent call last):
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\app.py", line 168, in <module>
    from layouts.main_layout import create_main_layout
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\layouts\main_layout.py", line 9, in <module>
    from layouts import COLORS, COMPONENTS, SPACING, TYPOGRAPHY
ImportError: cannot import name 'COLORS' from 'layouts' (c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\layouts\__init__.py)
2025-05-25 13:31:25 - __main__ - ERROR - Início do servidor abortado devido a falha na criação do layout ou registro de callbacks. [app.py:266]
2025-05-25 13:31:26 - __main__ - CRITICAL - ERRO CRÍTICO AO CRIAR LAYOUT [app.py:177]
Traceback (most recent call last):
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\app.py", line 168, in <module>
    from layouts.main_layout import create_main_layout
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\layouts\main_layout.py", line 9, in <module>
    from layouts import COLORS, COMPONENTS, SPACING, TYPOGRAPHY
ImportError: cannot import name 'COLORS' from 'layouts' (c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\layouts\__init__.py)
2025-05-25 13:31:26 - __main__ - CRITICAL - Erro inesperado durante a criação do layout: cannot import name 'COLORS' from 'layouts' (c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\layouts\__init__.py) [app.py:178]
Traceback (most recent call last):
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\app.py", line 168, in <module>
    from layouts.main_layout import create_main_layout
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\layouts\main_layout.py", line 9, in <module>
    from layouts import COLORS, COMPONENTS, SPACING, TYPOGRAPHY
ImportError: cannot import name 'COLORS' from 'layouts' (c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\layouts\__init__.py)
2025-05-25 13:31:26 - __main__ - ERROR - Início do servidor abortado devido a falha na criação do layout ou registro de callbacks. [app.py:266]
2025-05-25 13:32:22 - __main__ - CRITICAL - ERRO CRÍTICO AO CRIAR LAYOUT [app.py:177]
Traceback (most recent call last):
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\app.py", line 168, in <module>
    from layouts.main_layout import create_main_layout
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\layouts\main_layout.py", line 9, in <module>
    from layouts import COLORS, COMPONENTS, SPACING, TYPOGRAPHY
ImportError: cannot import name 'COLORS' from 'layouts' (c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\layouts\__init__.py)
2025-05-25 13:32:22 - __main__ - CRITICAL - Erro inesperado durante a criação do layout: cannot import name 'COLORS' from 'layouts' (c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\layouts\__init__.py) [app.py:178]
Traceback (most recent call last):
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\app.py", line 168, in <module>
    from layouts.main_layout import create_main_layout
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\layouts\main_layout.py", line 9, in <module>
    from layouts import COLORS, COMPONENTS, SPACING, TYPOGRAPHY
ImportError: cannot import name 'COLORS' from 'layouts' (c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\layouts\__init__.py)
2025-05-25 13:32:22 - __main__ - ERROR - Início do servidor abortado devido a falha na criação do layout ou registro de callbacks. [app.py:266]
2025-05-25 13:32:24 - __main__ - CRITICAL - ERRO CRÍTICO AO CRIAR LAYOUT [app.py:177]
Traceback (most recent call last):
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\app.py", line 168, in <module>
    from layouts.main_layout import create_main_layout
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\layouts\main_layout.py", line 9, in <module>
    from layouts import COLORS, COMPONENTS, SPACING, TYPOGRAPHY
ImportError: cannot import name 'COLORS' from 'layouts' (c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\layouts\__init__.py)
2025-05-25 13:32:24 - __main__ - CRITICAL - Erro inesperado durante a criação do layout: cannot import name 'COLORS' from 'layouts' (c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\layouts\__init__.py) [app.py:178]
Traceback (most recent call last):
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\app.py", line 168, in <module>
    from layouts.main_layout import create_main_layout
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\layouts\main_layout.py", line 9, in <module>
    from layouts import COLORS, COMPONENTS, SPACING, TYPOGRAPHY
ImportError: cannot import name 'COLORS' from 'layouts' (c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\layouts\__init__.py)
2025-05-25 13:32:24 - __main__ - ERROR - Início do servidor abortado devido a falha na criação do layout ou registro de callbacks. [app.py:266]
