/* assets/theme-light-vars.css - Consolidado */
:root {
    /* Cores principais */
    --light-primary: #26427A;
    --light-secondary: #6c757d;
    --light-accent: #007BFF;
    --light-accent-alt: #FFD700;

    /* Backgrounds */
    --light-background-main: #f0f2f5;
    --light-background-card: #ffffff;
    --light-background-card-header: #e9ecef;
    --light-background-input: #ffffff;
    --light-background-faint: #f5f5f5;

    /* Texto */
    --light-text-light: #f8f9fa;
    --light-text-dark: #212529;
    --light-text-muted: #6c757d;
    --light-text-header: #FFFFFF;

    /* Bordas */
    --light-border: #dee2e6;
    --light-border-light: #f8f9fa;
    --light-border-strong: #adb5bd;

    /* Status colors */
    --light-success: #198754;
    --light-danger: #dc3545;
    --light-warning: #ffc107;
    --light-info: #0dcaf0;

    /* Status backgrounds */
    --light-pass-bg: #d1e7dd;
    --light-fail-bg: #f8d7da;
    --light-warning-bg: #fff3cd;

    /* Elementos específicos */
    --light-input-focus-border: #007BFF;
    --light-input-focus-shadow: rgba(0, 123, 255, 0.25);
    --light-disabled-bg: #e9ecef;
}
