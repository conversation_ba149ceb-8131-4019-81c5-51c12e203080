# layouts/__init__.py
# Torna o diretório 'layouts' um pacote Python.
import logging

log = logging.getLogger(__name__)

# Importar estilos via bridge (sem fallback)
from utils.styles_bridge import COLORS, COMPONENTS, MESSAGE_STYLES, SPACING, TABLE_STYLES, TYPOGRAPHY
log.info("Layouts: Estilos importados via bridge JavaScript")

# Função para atualizar estilos a partir do store JavaScript
def update_styles_from_js(js_styles):
    """
    Atualiza os estilos Python a partir dos dados do store JavaScript.
    """
    global COLORS, COMPONENTS, MESSAGE_STYLES, SPACING, TABLE_STYLES, TYPOGRAPHY

    if not js_styles:
        log.warning("Dados de estilos JavaScript não disponíveis")
        return

    try:
        # Atualizar cada dicionário de estilo com os dados do JavaScript
        if 'colors' in js_styles and js_styles['colors']:
            COLORS.update(js_styles['colors'])
            log.debug("COLORS atualizado a partir do JavaScript")

        if 'components' in js_styles and js_styles['components']:
            COMPONENTS.update(js_styles['components'])
            log.debug("COMPONENTS atualizado a partir do JavaScript")

        if 'messageStyles' in js_styles and js_styles['messageStyles']:
            MESSAGE_STYLES.update(js_styles['messageStyles'])
            log.debug("MESSAGE_STYLES atualizado a partir do JavaScript")

        if 'spacing' in js_styles and js_styles['spacing']:
            SPACING.update(js_styles['spacing'])
            log.debug("SPACING atualizado a partir do JavaScript")

        if 'tableStyles' in js_styles and js_styles['tableStyles']:
            TABLE_STYLES.update(js_styles['tableStyles'])
            log.debug("TABLE_STYLES atualizado a partir do JavaScript")

        if 'typography' in js_styles and js_styles['typography']:
            TYPOGRAPHY.update(js_styles['typography'])
            log.debug("TYPOGRAPHY atualizado a partir do JavaScript")

        log.info("Todos os estilos Python atualizados a partir do JavaScript")
    except Exception as e:
        log.error(f"Erro ao atualizar estilos a partir do JavaScript: {e}")

# Exportar para uso nos layouts
__all__ = ["COLORS", "COMPONENTS", "MESSAGE_STYLES", "SPACING", "TABLE_STYLES", "TYPOGRAPHY", "update_styles_from_js"]
