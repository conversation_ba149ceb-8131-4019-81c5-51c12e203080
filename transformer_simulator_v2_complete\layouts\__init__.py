# layouts/__init__.py
# Torna o diretório 'layouts' um pacote Python.
import logging

log = logging.getLogger(__name__)

# Importar estilos via bridge
try:
    # Importar da ponte JavaScript -> Python
    from utils.styles_bridge import COLORS, COMPONENTS, MESSAGE_STYLES, SPACING, TABLE_STYLES, TYPOGRAPHY
    log.info("Layouts: Estilos importados via bridge JavaScript")
except ImportError as e:
    log.warning(f"Layouts: Falha ao importar via bridge: {e}")
    # Fallback para importação tradicional
    try:
        from utils.styles import COLORS, COMPONENTS, MESSAGE_STYLES, SPACING, TABLE_STYLES, TYPOGRAPHY
        log.info("Layouts: Estilos importados de utils.styles (legado)")
    except ImportError:
        log.error("Layouts: Falha ao importar estilos. Usando fallback mínimo.")
        # MIGRAÇÃO: Cores temporárias para compatibilidade
        COLORS = {
            "accent": "#26427A",
            "primary": "#26427A",
            "secondary": "#6c757d",
            "success": "#28a745",
            "info": "#17a2b8",
            "warning": "#ffc107",
            "danger": "#dc3545",
            "light": "#f8f9fa",
            "dark": "#343a40",
            "border": "#666666",
            "background_card": "#2c2c2c",
            "background_card_header": "#2a2a2a",
            "text_muted": "#6c757d",
            "text_light": "#f0f0f0",
            "background_main": "#1a1a1a",
        }
        COMPONENTS = {}
        MESSAGE_STYLES = {}
        SPACING = {}
        TABLE_STYLES = {}
        TYPOGRAPHY = {}

# Exportar para uso nos layouts
__all__ = ["COLORS", "COMPONENTS", "MESSAGE_STYLES", "SPACING", "TABLE_STYLES", "TYPOGRAPHY"]
