/* === CUSTOM STYLES - CONSOLIDADO === */

/* Utilitários gerais */
.help-tip {
    font-size: 0.8rem;
    color: #777;
    margin-left: 6px;
}

/* === TEMA ESCURO === */
body:not(.light-theme) {
    color: var(--dark-text-light);
    background-color: var(--dark-background-main);
}

/* Cards */
body:not(.light-theme) .card {
    background-color: var(--dark-background-card);
    border-color: var(--dark-border);
}

body:not(.light-theme) .card-header {
    background-color: var(--dark-background-card-header);
    border-color: var(--dark-border);
    color: var(--dark-text-header);
}

/* Inputs */
body:not(.light-theme) input,
body:not(.light-theme) select,
body:not(.light-theme) textarea {
    background-color: var(--dark-background-input);
    border: 1px solid var(--dark-border);
    color: var(--dark-text-light);
}

body:not(.light-theme) input:focus,
body:not(.light-theme) select:focus,
body:not(.light-theme) textarea:focus {
    background-color: #525252;
    border-color: var(--dark-input-focus-border);
    color: var(--dark-text-header);
    box-shadow: 0 0 0 0.2rem var(--dark-input-focus-shadow);
}

body:not(.light-theme) input:disabled {
    background-color: var(--dark-disabled-bg);
    border-color: var(--dark-border);
    color: var(--dark-text-muted);
}

body:not(.light-theme) input::placeholder,
body:not(.light-theme) textarea::placeholder {
    color: var(--dark-text-muted);
    opacity: 1;
}

/* Dropdowns */
body:not(.light-theme) .dropdown-menu {
    background-color: var(--dark-background-input);
    border-color: var(--dark-border);
}

body:not(.light-theme) .dropdown-item {
    color: var(--dark-text-light);
}

body:not(.light-theme) .dropdown-item:hover {
    background-color: #525252;
}

/* Tabelas */
body:not(.light-theme) .table {
    color: var(--dark-text-light);
}

body:not(.light-theme) .table thead th {
    background-color: var(--dark-background-card-header);
    color: var(--dark-text-header);
    border-color: var(--dark-border);
}

body:not(.light-theme) .table td {
    border-color: var(--dark-border);
}

/* Navegação */
body:not(.light-theme) .nav-link {
    color: var(--dark-text-light);
}

body:not(.light-theme) .navbar {
    background-color: var(--dark-background-card-header);
    border-bottom: 1px solid var(--dark-border);
}

body:not(.light-theme) .sidebar {
    background-color: var(--dark-background-card);
    border-right: 1px solid var(--dark-border);
}

/* Checkboxes */
body:not(.light-theme) .override-checkbox .form-check-label {
    color: var(--dark-text-light);
}

/* Modais */
body:not(.light-theme) .modal-content {
    background-color: var(--dark-background-card);
    border-color: var(--dark-border);
    color: var(--dark-text-light);
}

body:not(.light-theme) .modal-header {
    background-color: var(--dark-background-card-header);
    border-color: var(--dark-border);
    color: var(--dark-text-header);
}

body:not(.light-theme) .modal-footer {
    border-color: var(--dark-border);
}

body:not(.light-theme) .close {
    color: var(--dark-text-light);
    text-shadow: none;
}

body:not(.light-theme) .close:hover {
    color: var(--dark-text-header);
}

/* Alertas */
body:not(.light-theme) .alert {
    background-color: var(--dark-background-card);
    border-color: var(--dark-border);
    color: var(--dark-text-light);
}

/* Tooltips */
body:not(.light-theme) .tooltip-inner {
    background-color: var(--dark-background-card-header);
    color: var(--dark-text-light);
}

body:not(.light-theme) .popover {
    background-color: var(--dark-background-card);
    border-color: var(--dark-border);
}

body:not(.light-theme) .popover-header {
    background-color: var(--dark-background-card-header);
    border-color: var(--dark-border);
    color: var(--dark-text-header);
}

body:not(.light-theme) .popover-body {
    color: var(--dark-text-light);
}

/* Dash components */
body:not(.light-theme) .dash-spreadsheet-inner th {
    background-color: var(--dark-background-card-header);
    color: var(--dark-text-header);
}

body:not(.light-theme) .dash-spreadsheet-inner td {
    background-color: var(--dark-background-card);
    color: var(--dark-text-light);
}

/* === TEMA CLARO === */

/* Inputs */
body.light-theme input,
body.light-theme select,
body.light-theme textarea {
    background-color: var(--light-background-input);
    border: 1px solid var(--light-border);
    color: var(--light-text-dark);
}

body.light-theme input:focus,
body.light-theme select:focus,
body.light-theme textarea:focus {
    background-color: var(--light-background-input);
    border-color: var(--light-input-focus-border);
    color: var(--light-text-dark);
    box-shadow: 0 0 0 0.2rem var(--light-input-focus-shadow);
}

body.light-theme input:disabled,
body.light-theme .disabled-field:disabled {
    background-color: var(--light-disabled-bg);
    color: var(--light-text-muted);
    cursor: not-allowed;
}

body.light-theme input::placeholder,
body.light-theme textarea::placeholder {
    color: var(--light-text-muted);
    opacity: 1;
}

/* Labels */
body.light-theme .form-label,
body.light-theme label {
    color: var(--light-text-dark);
}

/* Cards */
body.light-theme .card {
    background-color: var(--light-background-card);
    border-color: var(--light-border);
    color: var(--light-text-dark);
}

body.light-theme .card-header {
    background-color: var(--light-background-card-header);
    border-color: var(--light-border);
    color: var(--light-text-dark);
}
