# utils/styles_js_bridge.py
"""
Ponte para obter estilos JavaScript via Dash Clientside Callbacks.
Este módulo permite integração entre JavaScript e Python para estilos.
"""
import json
import logging

import dash
from dash import Output, Input, html, dcc, clientside_callback, ClientsideFunction
from dash.exceptions import PreventUpdate

log = logging.getLogger(__name__)


def register_style_clientside_callbacks(app):
    """
    Registra os callbacks clientside para integração dos estilos JS com Python.
    """
    try:
        # Callback que obtém estilos JavaScript e os disponibiliza para Python
        clientside_callback(
            ClientsideFunction('styles_integration', 'getStyles'),
            Output('styles-store', 'data'),
            Input('styles-store-trigger', 'n_intervals'),
        )
        log.info("Callbacks clientside de estilos registrados com sucesso")
    except Exception as e:
        log.error(f"Erro ao registrar callbacks clientside de estilos: {e}")


def create_style_stores():
    """
    Cria os componentes de store necessários para a comunicação de estilos JS <-> Python.
    """
    return html.Div(
        [
            dcc.Store(id='styles-store', storage_type='memory'),
            dcc.Interval(id='styles-store-trigger', interval=1000, max_intervals=1),
        ],
        style={'display': 'none'}
    )


def register_theme_callback(app):
    """
    Registra o callback clientside para alternar entre temas claro e escuro.
    Essa função deve ser chamada uma vez durante a inicialização da aplicação.
    """
    try:
        app.clientside_callback(
            ClientsideFunction('styles_integration', 'setTheme'),
            Output('theme-status', 'data'),
            Input('theme-name', 'data'),
        )
        log.info("Callback de tema registrado com sucesso")
        return True
    except Exception as e:
        log.error(f"Erro ao registrar callback de tema: {e}")
        return False

def set_theme(theme_name):
    """
    Aciona a alteração de tema. Esta função emula a interface da função set_theme
    de styles_bridge.py para manter compatibilidade.
    """
    try:
        log.info(f"Solicitando alteração para tema: {theme_name}")
        # Esta função apenas emula a interface, o trabalho real é feito pelo clientside callback
        return {"status": "requested", "theme": theme_name}
    except Exception as e:
        log.error(f"Erro ao solicitar alteração de tema: {e}")
        return {"status": "error", "message": str(e)}
