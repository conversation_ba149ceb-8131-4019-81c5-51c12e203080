# app_core/state_manager.py
"""
StateManager - Wrapper around TransformerMCP to provide a unified interface
for state management across the application.
"""
import logging
from typing import Dict, Any, Optional
from .transformer_mcp import TransformerMCP

log = logging.getLogger(__name__)


class StateManager:
    """
    StateManager provides a unified interface for state management,
    wrapping the TransformerMCP to provide both get_store/set_store
    and get_data/set_data methods for compatibility.
    """

    def __init__(self, load_from_disk: bool = False):
        """
        Initialize the StateManager with an underlying TransformerMCP.

        Args:
            load_from_disk: Whether to load existing data from disk
        """
        log.info(f"Initializing StateManager (load_from_disk={load_from_disk})")
        self.mcp = TransformerMCP(load_from_disk=load_from_disk)
        log.info("StateManager initialized successfully")

    # MCP-compatible interface
    def get_data(self, store_id: str, force_reload: bool = False) -> Dict[str, Any]:
        """
        Get data from a store (MCP-compatible interface).

        Args:
            store_id: ID of the store
            force_reload: Whether to force reload from disk

        Returns:
            Store data or empty dict if not found
        """
        return self.mcp.get_data(store_id, force_reload)

    def set_data(self, store_id: str, data: Dict[str, Any], auto_propagate: bool = False) -> None:
        """
        Set data in a store (MCP-compatible interface).

        Args:
            store_id: ID of the store
            data: Data to set
            auto_propagate: Whether to auto-propagate changes
        """
        self.mcp.set_data(store_id, data, auto_propagate)

    # StateManager-specific interface (aliases for compatibility)
    def get_store(self, store_id: str, force_reload: bool = False) -> Dict[str, Any]:
        """
        Get data from a store (StateManager interface).
        This is an alias for get_data() for compatibility.

        Args:
            store_id: ID of the store
            force_reload: Whether to force reload from disk

        Returns:
            Store data or empty dict if not found
        """
        return self.get_data(store_id, force_reload)

    def set_store(self, store_id: str, data: Dict[str, Any], auto_propagate: bool = False) -> None:
        """
        Set data in a store (StateManager interface).
        This is an alias for set_data() for compatibility.

        Args:
            store_id: ID of the store
            data: Data to set
            auto_propagate: Whether to auto-propagate changes
        """
        self.set_data(store_id, data, auto_propagate)

    # Delegate other methods to the underlying MCP
    def add_listener(self, store_id: str, callback):
        """Add a listener for store changes."""
        return self.mcp.add_listener(store_id, callback)

    def remove_listener(self, store_id: str, callback):
        """Remove a listener for store changes."""
        return self.mcp.remove_listener(store_id, callback)

    def save_to_disk(self) -> bool:
        """Save current state to disk."""
        return self.mcp.save_to_disk()

    def load_from_disk(self) -> bool:
        """Load state from disk."""
        return self.mcp._load_from_disk()

    def save_session(self, session_name: str, notes: str = "", stores_data: Optional[Dict[str, Any]] = None) -> int:
        """Save a test session."""
        return self.mcp.save_session(session_name, notes, stores_data)

    def get_all_stores(self) -> Dict[str, Dict[str, Any]]:
        """Get all store data."""
        return self.mcp.get_all_data()

    def clear_store(self, store_id: str) -> bool:
        """Clear a specific store."""
        # Clear a specific store by setting it to empty dict
        self.mcp.set_data(store_id, {})
        return True

    def clear_all_stores(self) -> bool:
        """Clear all stores."""
        self.mcp.clear_data()
        return True

    @property
    def last_save_error(self):
        """Get the last save error from the underlying MCP."""
        return self.mcp.last_save_error