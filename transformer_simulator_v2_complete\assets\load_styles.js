/**
 * load_styles.js
 * Este script é responsável por carregar os arquivos de estilo JavaScript
 * e disponibilizá-los para os componentes Dash.
 */

// Função para carregar um script JS dinamicamente
function loadScript(url, callback) {
    var script = document.createElement('script');
    script.type = 'text/javascript';
    script.src = url;
    
    // Quando o script for carregado, execute o callback
    if (callback) {
        script.onload = callback;
    }
    
    // Adiciona o script ao documento
    document.head.appendChild(script);
}

// Carregando scripts de estilo na ordem correta
window.addEventListener('DOMContentLoaded', (event) => {
    console.log('Carregando scripts de estilo...');
    
    // Primeiro carrega theme_colors.js
    loadScript('/assets/theme_colors.js', function() {
        console.log('theme_colors.js carregado');
        
        // Depois carrega styles.js que depende de theme_colors.js
        loadScript('/assets/styles.js', function() {
            console.log('styles.js carregado');
            
            // Aplica o tema atual com base na classe do body
            if (document.body.classList.contains('light-theme')) {
                window.setTheme('light');
            } else {
                window.setTheme('dark');
            }
            
            // Dispara um evento para notificar que os estilos foram carregados
            document.dispatchEvent(new CustomEvent('styles_loaded'));
        });
    });
});
