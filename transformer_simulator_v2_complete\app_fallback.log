2025-05-25 13:40:25 - ERROR - Erro ao incrementar contador de uso: No module named 'utils.styles'
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\utils\usage_tracker.py", line 118, in increment_counter
    import config
  File "C:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\config.py", line 67, in <module>
    from utils.styles import COLORS, COMPONENTS, MESSAGE_STYLES, SPACING, TABLE_STYLES, TYPOGRAPHY
ModuleNotFoundError: No module named 'utils.styles'
2025-05-25 13:40:25 - CRITICAL - Não foi possível importar config.py. Usando configuração de fallback.
2025-05-25 13:40:25 - ERROR - Erro ao incrementar contador de uso: No module named 'utils.styles'
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\utils\usage_tracker.py", line 118, in increment_counter
    import config
  File "C:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\config.py", line 67, in <module>
    from utils.styles import COLORS, COMPONENTS, MESSAGE_STYLES, SPACING, TABLE_STYLES, TYPOGRAPHY
ModuleNotFoundError: No module named 'utils.styles'
2025-05-25 13:40:25 - ERROR - Erro ao importar módulo de callback losses: No module named 'utils.styles'
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\app.py", line 201, in <module>
    __import__(module_path)
  File "C:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\callbacks\losses.py", line 29, in <module>
    from config import colors as CONFIG_COLORS
  File "C:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\config.py", line 67, in <module>
    from utils.styles import COLORS, COMPONENTS, MESSAGE_STYLES, SPACING, TABLE_STYLES, TYPOGRAPHY
ModuleNotFoundError: No module named 'utils.styles'
2025-05-25 13:40:25 - ERROR - Erro ao importar módulo de callback dieletric_analysis: No module named 'utils.styles'
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\app.py", line 201, in <module>
    __import__(module_path)
  File "C:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\callbacks\dieletric_analysis.py", line 12, in <module>
    import config
  File "C:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\config.py", line 67, in <module>
    from utils.styles import COLORS, COMPONENTS, MESSAGE_STYLES, SPACING, TABLE_STYLES, TYPOGRAPHY
ModuleNotFoundError: No module named 'utils.styles'
2025-05-25 13:40:25 - ERROR - Erro ao importar módulo de callback report_generation: No module named 'utils.styles'
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\app.py", line 201, in <module>
    __import__(module_path)
  File "C:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\callbacks\report_generation.py", line 24, in <module>
    from utils.pdf_generator import generate_pdf
  File "C:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\utils\pdf_generator.py", line 24, in <module>
    import config
  File "C:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\config.py", line 67, in <module>
    from utils.styles import COLORS, COMPONENTS, MESSAGE_STYLES, SPACING, TABLE_STYLES, TYPOGRAPHY
ModuleNotFoundError: No module named 'utils.styles'
2025-05-25 13:40:25 - ERROR - Erro ao importar módulo de callback dielectric_analysis_comprehensive: No module named 'utils.styles'
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\app.py", line 201, in <module>
    __import__(module_path)
  File "C:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\callbacks\dielectric_analysis_comprehensive.py", line 12, in <module>
    from components.ui_elements import create_comparison_table
  File "C:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\components\ui_elements.py", line 12, in <module>
    import config
  File "C:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\config.py", line 67, in <module>
    from utils.styles import COLORS, COMPONENTS, MESSAGE_STYLES, SPACING, TABLE_STYLES, TYPOGRAPHY
ModuleNotFoundError: No module named 'utils.styles'
2025-05-25 13:40:26 - ERROR - Erro ao importar módulo para registro explícito short_circuit: No module named 'utils.styles'
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\app.py", line 224, in <module>
    module = __import__(module_path, fromlist=[reg_func_name])
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\callbacks\short_circuit.py", line 15, in <module>
    from config import colors  # Importar cores para estilos de status
    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\config.py", line 67, in <module>
    from utils.styles import COLORS, COMPONENTS, MESSAGE_STYLES, SPACING, TABLE_STYLES, TYPOGRAPHY
ModuleNotFoundError: No module named 'utils.styles'
