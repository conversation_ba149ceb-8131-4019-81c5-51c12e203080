/* assets/theme-dark-vars.css */
:root {
    --dark-primary: #26427A;
    --dark-secondary: #6c757d;
    --dark-accent: #00BFFF; /* Cyan */
    --dark-accent-alt: #FFD700; /* Gold */
    --dark-background-main: #1a1a1a;
    --dark-background-card: #343a40; /* Ajustado para tornar cards levemente mais claros e visíveis */
    --dark-background-card-header: #1f1f1f;
    --dark-background-input: #3a3a3a; /* Revertido para tom anterior, melhor contraste */
    --dark-background-header: #1f1f1f;
    --dark-background-faint: #333333;
    --dark-text-light: #f0f0f0;
    --dark-text-dark: #f0f0f0; /* Corrigido: texto escuro no tema escuro deve ser claro */
    --dark-text-muted: #b0b0b0;
    --dark-text-header: #FFFFFF;
    --dark-border: #666666;
    --dark-border-light: #777777;
    --dark-border-strong: #888888;
    --dark-success: #28a745;
    --dark-danger: #dc3545;
    --dark-warning: #ffc107;
    --dark-info: #00BFFF; /* Cyan */
    --dark-pass: #28a745; /* Alias para success */
    --dark-fail: #dc3545; /* Alias para danger */
    --dark-pass-bg: rgba(40, 167, 69, 0.2);
    --dark-fail-bg: rgba(220, 53, 69, 0.2);
    --dark-warning-bg: rgba(255, 193, 7, 0.2);
    --dark-danger-bg-opaque: #5c1c1c; /* Opaque red for table status */
    --dark-warning-bg-faint-opaque: rgba(255, 193, 7, 0.3);
    --dark-warning-high-bg-faint-opaque: rgba(255, 165, 0, 0.3);
    --dark-ok-bg-faint-opaque: rgba(40, 167, 69, 0.3);
    --dark-info-bg-faint-opaque: rgba(0, 191, 255, 0.2);

    /* Adicionando variáveis para elementos específicos */
    --dark-input-text: #e0e0e0;
    --dark-input-placeholder: #888888;
    --dark-dropdown-bg: #343a40; /* Dropdown agora com tom levemente mais claro */
    --dark-dropdown-text: #e0e0e0;
    --dark-dropdown-hover-bg: #3a3a3a;
    --dark-card-text: #e0e0e0;
    --dark-link-color: #00BFFF;
    --dark-link-hover: #99e6ff;
    --dark-table-header-bg: #1f1f1f;
    --dark-table-header-text: #FFFFFF;
    --dark-table-row-bg: #2c2c2c;
    --dark-table-row-alt-bg: #333333;
    --dark-table-border: #444444;
    --dark-code-bg: #2d2d2d;
    --dark-code-text: #f8f8f2;

    /* Adicionando variáveis para elementos específicos com problemas de contraste */
    --dark-button-text: #FFFFFF;
    --dark-button-bg: #26427A;
    --dark-button-hover-bg: #345694;
    --dark-button-active-bg: #1d325d;
    --dark-input-focus-border: #00BFFF;
    --dark-input-focus-shadow: rgba(0, 191, 255, 0.25);
    --dark-label-text: #e0e0e0;
    --dark-disabled-text: #a0a0a0;
    --dark-disabled-bg: #2a2a2a;
}
