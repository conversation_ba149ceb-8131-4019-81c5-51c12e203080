/* assets/theme-dark-vars.css - CONSOLIDADO */
:root {
    /* Cores principais */
    --dark-primary: #26427A;
    --dark-secondary: #6c757d;
    --dark-accent: #00BFFF;
    --dark-accent-alt: #FFD700;

    /* Backgrounds */
    --dark-background-main: #1a1a1a;
    --dark-background-card: #2c2c2c;
    --dark-background-card-header: #2a2a2a;
    --dark-background-input: #4a4a4a;
    --dark-background-faint: #333333;

    /* Texto */
    --dark-text-light: #f0f0f0;
    --dark-text-dark: #f0f0f0;
    --dark-text-muted: #b0b0b0;
    --dark-text-header: #FFFFFF;

    /* Bordas */
    --dark-border: #666666;
    --dark-border-light: #777777;
    --dark-border-strong: #888888;

    /* Status colors */
    --dark-success: #28a745;
    --dark-danger: #dc3545;
    --dark-warning: #ffc107;
    --dark-info: #00BFFF;

    /* Status backgrounds */
    --dark-pass-bg: rgba(40, 167, 69, 0.2);
    --dark-fail-bg: rgba(220, 53, 69, 0.2);
    --dark-warning-bg: rgba(255, 193, 7, 0.2);

    /* Elementos específicos */
    --dark-input-focus-border: #00BFFF;
    --dark-input-focus-shadow: rgba(0, 191, 255, 0.25);
    --dark-disabled-bg: #2a2a2a;
}
