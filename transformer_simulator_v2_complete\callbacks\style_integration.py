# callbacks/style_integration.py
"""
Callbacks para integração dos estilos JavaScript com Python.
"""
import logging

import dash
from dash import Input, Output, callback, ctx, State
from dash.exceptions import PreventUpdate

log = logging.getLogger(__name__)

# Tentar importar as funções de atualização de estilos
try:
    from layouts import update_styles_from_js
    from utils.styles import set_theme
    HAS_STYLE_FUNCTIONS = True
    log.info("Funções de integração de estilos carregadas")
except ImportError as e:
    HAS_STYLE_FUNCTIONS = False
    log.error(f"Não foi possível importar funções de integração de estilos: {e}")


@callback(
    Output("style-integration-status", "data", allow_duplicate=True),
    Input("styles-store", "data"),
    State("theme-name", "data"),
    prevent_initial_call=True
)
def update_python_styles_from_js(js_styles, theme_name):
    """
    Callback que é acionado quando os estilos JavaScript são carregados.
    Atualiza os estilos Python com base nos dados do JavaScript.
    """
    if not js_styles:
        log.warning("Dados de estilos JavaScript vazios ou não disponíveis")
        raise PreventUpdate
    
    if not HAS_STYLE_FUNCTIONS:
        log.warning("Funções de integração de estilos não disponíveis")
        raise PreventUpdate
        
    try:
        # Aplicar o tema atual conforme armazenado no store
        theme = theme_name or 'dark'
        log.info(f"Aplicando tema via callback: {theme}")
        set_theme(theme)
        
        # Atualizar todos os estilos Python
        update_styles_from_js(js_styles)
        log.info(f"Estilos Python atualizados a partir dos dados JavaScript")
        
        return {"status": "updated", "timestamp": dash.get_asset_url('theme_colors.js')}
    except Exception as e:
        log.error(f"Erro ao atualizar estilos Python: {e}")
        return {"status": "error", "error": str(e)}


@callback(
    Output("theme-name", "data"),
    Output("style-integration-status", "data"),
    Input("theme-toggle", "n_clicks"),
    State("theme-name", "data"),
    prevent_initial_call=True
)
def toggle_theme(n_clicks, current_theme):
    """
    Callback que alterna entre os temas claro e escuro.
    Atualiza o store com o novo tema e aciona uma atualização de estilos.
    """
    if n_clicks is None:
        raise PreventUpdate
    
    # Alternar tema
    new_theme = 'light' if current_theme == 'dark' else 'dark'
    log.info(f"Alternando tema: {current_theme} -> {new_theme}")
    
    if HAS_STYLE_FUNCTIONS:
        try:
            # Aplicar o tema via função de estilo
            set_theme(new_theme)
            log.info(f"Tema aplicado via set_theme: {new_theme}")
        except Exception as e:
            log.error(f"Erro ao aplicar tema: {e}")
    
    return new_theme, {"status": "theme_changed", "theme": new_theme}
