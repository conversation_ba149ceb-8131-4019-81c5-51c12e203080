2025-05-25 00:33:20 - callbacks.temperature_rise - INFO - Registrando callbacks do módulo temperature_rise para app Simulador de Testes de Transformadores (IEC/IEEE/ABNT) EPS 1500...
2025-05-25 00:33:20 - callbacks.temperature_rise - INFO - Callbacks do módulo temperature_rise já registrados via decoradores @app.callback.
2025-05-25 00:33:20 - app - INFO - Callbacks registrados. Total: 50
2025-05-25 00:33:20 - callbacks.insulation_level_callbacks - INFO - Registrando callbacks de níveis de isolamento (APENAS OPÇÕES - V2)...
2025-05-25 00:33:20 - callbacks.insulation_level_callbacks - INFO - Callbacks de níveis de isolamento (APENAS OPÇÕES - V2) registrados.
2025-05-25 00:33:20 - callbacks.transformer_inputs - INFO - Registrando callbacks do módulo transformer_inputs para app Simulador de Testes de Transformadores (IEC/IEEE/ABNT) EPS 1500...
2025-05-25 00:33:20 - callbacks.temperature_rise - INFO - Registrando callbacks do módulo temperature_rise para app Simulador de Testes de Transformadores (IEC/IEEE/ABNT) EPS 1500...
2025-05-25 00:33:20 - callbacks.temperature_rise - INFO - Callbacks do módulo temperature_rise já registrados via decoradores @app.callback.
2025-05-25 00:33:20 - __main__ - INFO - Callbacks registrados. Total: 35
2025-05-25 00:33:20 - __main__ - INFO - Iniciando servidor Dash em http://127.0.0.1:8050/ (Debug: True)
2025-05-25 00:33:20 - dash.dash - INFO - Dash is running on http://127.0.0.1:8050/

2025-05-25 00:33:23 - callbacks.global_updates - WARNING - Dados essenciais ausentes no transformer_store_data
2025-05-25 00:33:23 - components.transformer_info_template - WARNING - transformer_data inválido ou vazio: {}
2025-05-25 00:33:23 - layouts.transformer_inputs - INFO - Criando layout Dados Básicos (v5 - Layout Geral e Pesos em 2 linhas)...
2025-05-25 00:33:23 - callbacks.global_updates - DEBUG - Painel atualizado em 1.01ms
2025-05-25 00:33:23 - layouts.transformer_inputs - INFO - Classes de tensão carregadas para dropdown: 27 opções.
2025-05-25 00:33:23 - callbacks.global_updates - WARNING - Dados essenciais ausentes no transformer_store_data
2025-05-25 00:33:23 - components.transformer_info_template - WARNING - transformer_data inválido ou vazio: {}
2025-05-25 00:33:23 - callbacks.global_updates - DEBUG - Painel atualizado em 0.0ms
2025-05-25 00:33:23 - callbacks.transformer_inputs - INFO - [LOAD TransformerInputs] Acionado por: None
2025-05-25 00:33:23 - callbacks.transformer_inputs - INFO - [LOAD TransformerInputs] Opções dos dropdowns ainda não foram populadas, aguardando...
2025-05-25 00:33:23 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_kv: 69, Tensão: 69.1, Path: /dados
2025-05-25 00:33:23 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT] Valores do MCP: NBI=None, SIL=None, TA=None, TI=None
2025-05-25 00:33:23 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT-NEUTRO] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_Neutro: None, Conexão: triangulo, Tensão: 69.1
2025-05-25 00:33:23 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT] Valores salvos: NBI=None, SIL=None, TA=None, TI=
2025-05-25 00:33:23 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_kv: 3.6, Tensão: 13.8, Path: /dados
2025-05-25 00:33:23 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT-NEUTRO] Valores do MCP: NBI=None, SIL=None
2025-05-25 00:33:23 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT-NEUTRO] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_Neutro: None, Conexão: triangulo, Tensão: 13.8
2025-05-25 00:33:23 - app_core.isolation_repo - WARNING - [ISOLATION] Nenhum registro encontrado para Um=69.0kV e norma=IEC/NBR. Usando valores padrão.
2025-05-25 00:33:23 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT] Valores do MCP: NBI=None, SIL=None, TA=None, TI=None
2025-05-25 00:33:23 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_kv: 24, Tensão: 13, Path: /dados
2025-05-25 00:33:23 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT-NEUTRO] Valores salvos: NBI=None, SIL=None
2025-05-25 00:33:23 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT-NEUTRO] Valores do MCP: NBI=None, SIL=None
2025-05-25 00:33:23 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO-NEUTRO] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_Neutro: None, Conexão: triangulo, Tensão: 13
2025-05-25 00:33:23 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - AT] Opções FILTRADAS por Um=69.0 e Norma=IEC.
2025-05-25 00:33:23 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT] Valores salvos: NBI=None, SIL=None, TA=None, TI=/dados
2025-05-25 00:33:23 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO] Valores do MCP: NBI=None, SIL=None, TA=None, TI=None
2025-05-25 00:33:23 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT-NEUTRO] Valores salvos: NBI=None, SIL=None
2025-05-25 00:33:23 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO-NEUTRO] Valores do MCP: NBI=None, SIL=None
2025-05-25 00:33:23 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO] Valores salvos: NBI=None, SIL=None, TA=None, TI=/dados
2025-05-25 00:33:23 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - BT] Opções FILTRADAS por Um=3.6 e Norma=IEC.
2025-05-25 00:33:23 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO-NEUTRO] Valores salvos: NBI=None, SIL=None
2025-05-25 00:33:23 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - TERCIARIO] Opções FILTRADAS por Um=24.0 e Norma=IEC.
2025-05-25 00:33:27 - callbacks.transformer_inputs - INFO - [LOAD TransformerInputs] Acionado por: url
2025-05-25 00:33:27 - callbacks.transformer_inputs - DEBUG - [LOAD TransformerInputs] Não estamos na página de dados (pathname=/perdas, clean_path=perdas), prevenindo atualização
2025-05-25 00:33:27 - utils.mcp_persistence - INFO - [ensure_mcp_data_propagation] Store de origem transformer-inputs-store É a fonte-de-verdade para campos básicos
2025-05-25 00:33:27 - app_core.transformer_mcp - DEBUG - [MCP SET] Dados definidos para store 'losses-store'.
2025-05-25 00:33:27 - utils.mcp_persistence - INFO - [ensure_mcp_data_propagation] Dados básicos propagados para losses-store
2025-05-25 00:33:27 - layouts.losses - INFO - Creating Losses layout...
2025-05-25 00:33:27 - layouts.losses - INFO - [Losses Layout] Dados do transformador obtidos do losses-store.
2025-05-25 00:33:27 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT] Acionado por: url. Norma: IEC, Um_kv: 69, Tensão: 69.1, Path: /perdas
2025-05-25 00:33:27 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT] Acionado por: url. Norma: IEC, Um_kv: 3.6, Tensão: 13.8, Path: /perdas
2025-05-25 00:33:27 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT] Valores do MCP: NBI=None, SIL=None, TA=None, TI=None
2025-05-25 00:33:27 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO] Acionado por: url. Norma: IEC, Um_kv: 24, Tensão: 13, Path: /perdas
2025-05-25 00:33:27 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT] Valores do MCP: NBI=None, SIL=None, TA=None, TI=None
2025-05-25 00:33:27 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT] Valores salvos: NBI=None, SIL=None, TA=None, TI=
2025-05-25 00:33:27 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT-NEUTRO] Acionado por: url. Norma: IEC, Um_Neutro: None, Conexão: triangulo, Tensão: 69.1
2025-05-25 00:33:27 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO] Valores do MCP: NBI=None, SIL=None, TA=None, TI=None
2025-05-25 00:33:27 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT] Valores salvos: NBI=None, SIL=None, TA=None, TI=/perdas
2025-05-25 00:33:27 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT-NEUTRO] Acionado por: url. Norma: IEC, Um_Neutro: None, Conexão: triangulo, Tensão: 13.8
2025-05-25 00:33:27 - app_core.isolation_repo - WARNING - [ISOLATION] Nenhum registro encontrado para Um=69.0kV e norma=IEC/NBR. Usando valores padrão.
2025-05-25 00:33:27 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT-NEUTRO] Valores do MCP: NBI=None, SIL=None
2025-05-25 00:33:27 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO-NEUTRO] Acionado por: url. Norma: IEC, Um_Neutro: None, Conexão: triangulo, Tensão: 13
2025-05-25 00:33:27 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO] Valores salvos: NBI=None, SIL=None, TA=None, TI=/perdas
2025-05-25 00:33:27 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT-NEUTRO] Valores do MCP: NBI=None, SIL=None
2025-05-25 00:33:27 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - AT] Opções FILTRADAS por Um=69.0 e Norma=IEC.
2025-05-25 00:33:27 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT-NEUTRO] Valores salvos: NBI=None, SIL=None
2025-05-25 00:33:27 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO-NEUTRO] Valores do MCP: NBI=None, SIL=None
2025-05-25 00:33:27 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - BT] Opções FILTRADAS por Um=3.6 e Norma=IEC.
2025-05-25 00:33:27 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT-NEUTRO] Valores salvos: NBI=None, SIL=None
2025-05-25 00:33:27 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - TERCIARIO] Opções FILTRADAS por Um=24.0 e Norma=IEC.
2025-05-25 00:33:27 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO-NEUTRO] Valores salvos: NBI=None, SIL=None
2025-05-25 00:33:27 - callbacks.global_updates - WARNING - Dados essenciais ausentes no transformer_store_data
2025-05-25 00:33:27 - components.transformer_info_template - WARNING - transformer_data inválido ou vazio: {}
2025-05-25 00:33:27 - callbacks.global_updates - DEBUG - Painel atualizado em 3.0ms
2025-05-25 00:33:27 - callbacks.losses - CRITICAL - [LOSSES POPULATE VAZIO] Acionado. Aba: tab-vazio, Trigger: N/A
2025-05-25 00:33:33 - layouts.transformer_inputs - INFO - Criando layout Dados Básicos (v5 - Layout Geral e Pesos em 2 linhas)...
2025-05-25 00:33:33 - layouts.transformer_inputs - INFO - Classes de tensão carregadas para dropdown: 27 opções.
2025-05-25 00:33:33 - callbacks.global_updates - WARNING - Dados essenciais ausentes no transformer_store_data
2025-05-25 00:33:33 - components.transformer_info_template - WARNING - transformer_data inválido ou vazio: {}
2025-05-25 00:33:33 - callbacks.transformer_inputs - INFO - [LOAD TransformerInputs] Acionado por: None
2025-05-25 00:33:33 - callbacks.global_updates - DEBUG - Painel atualizado em 1.0ms
2025-05-25 00:33:33 - callbacks.transformer_inputs - INFO - [LOAD TransformerInputs] Opções dos dropdowns ainda não foram populadas, aguardando...
2025-05-25 00:33:33 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_kv: 69, Tensão: 69.1, Path: /dados
2025-05-25 00:33:33 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT] Valores do MCP: NBI=None, SIL=None, TA=None, TI=None
2025-05-25 00:33:33 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT-NEUTRO] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_Neutro: None, Conexão: triangulo, Tensão: 69.1
2025-05-25 00:33:33 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_kv: 3.6, Tensão: 13.8, Path: /dados
2025-05-25 00:33:33 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT] Valores salvos: NBI=None, SIL=None, TA=None, TI=
2025-05-25 00:33:33 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT-NEUTRO] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_Neutro: None, Conexão: triangulo, Tensão: 13.8
2025-05-25 00:33:33 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT-NEUTRO] Valores do MCP: NBI=None, SIL=None
2025-05-25 00:33:33 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_kv: 24, Tensão: 13, Path: /dados
2025-05-25 00:33:33 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT] Valores do MCP: NBI=None, SIL=None, TA=None, TI=None
2025-05-25 00:33:33 - app_core.isolation_repo - WARNING - [ISOLATION] Nenhum registro encontrado para Um=69.0kV e norma=IEC/NBR. Usando valores padrão.
2025-05-25 00:33:33 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO-NEUTRO] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_Neutro: None, Conexão: triangulo, Tensão: 13
2025-05-25 00:33:33 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT-NEUTRO] Valores do MCP: NBI=None, SIL=None
2025-05-25 00:33:33 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT-NEUTRO] Valores salvos: NBI=None, SIL=None
2025-05-25 00:33:33 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO] Valores do MCP: NBI=None, SIL=None, TA=None, TI=None
2025-05-25 00:33:33 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT] Valores salvos: NBI=None, SIL=None, TA=None, TI=/dados
2025-05-25 00:33:33 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - AT] Opções FILTRADAS por Um=69.0 e Norma=IEC.
2025-05-25 00:33:33 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO-NEUTRO] Valores do MCP: NBI=None, SIL=None
2025-05-25 00:33:33 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT-NEUTRO] Valores salvos: NBI=None, SIL=None
2025-05-25 00:33:33 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO] Valores salvos: NBI=None, SIL=None, TA=None, TI=/dados
2025-05-25 00:33:33 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - BT] Opções FILTRADAS por Um=3.6 e Norma=IEC.
2025-05-25 00:33:33 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO-NEUTRO] Valores salvos: NBI=None, SIL=None
2025-05-25 00:33:33 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - TERCIARIO] Opções FILTRADAS por Um=24.0 e Norma=IEC.
2025-05-25 00:34:32 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT] Acionado por: tensao_at. Norma: IEC, Um_kv: 69, Tensão: 69, Path: /dados
2025-05-25 00:34:32 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT-NEUTRO] Acionado por: tensao_at. Norma: IEC, Um_Neutro: None, Conexão: triangulo, Tensão: 69
2025-05-25 00:34:32 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT] Valores do MCP: NBI=None, SIL=None, TA=None, TI=None
2025-05-25 00:34:32 - callbacks.transformer_inputs - DEBUG - [UpdateTransformerCalc] Acionado por: tensao_at
2025-05-25 00:34:32 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT-NEUTRO] Valores do MCP: NBI=None, SIL=None
2025-05-25 00:34:32 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT] Valores salvos: NBI=None, SIL=None, TA=None, TI=
2025-05-25 00:34:32 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT-NEUTRO] Valores salvos: NBI=None, SIL=None
2025-05-25 00:34:32 - app_core.isolation_repo - WARNING - [ISOLATION] Nenhum registro encontrado para Um=69.0kV e norma=IEC/NBR. Usando valores padrão.
2025-05-25 00:34:32 - callbacks.transformer_inputs - INFO - [UpdateTransformerCalc] Sincronizando elevação de enrolamento: AT=BT=Terciário=55°C
2025-05-25 00:34:32 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - AT] Opções FILTRADAS por Um=69.0 e Norma=IEC.
2025-05-25 00:34:32 - app_core.transformer_mcp - DEBUG - [MCP SET] Dados definidos para store 'transformer-inputs-store'.
2025-05-25 00:34:32 - app_core.transformer_mcp - INFO - [MCP SAVE_TO_DISK] Salvando estado do MCP em disco...
2025-05-25 00:34:32 - app_core.transformer_mcp - INFO - [MCP SAVE_TO_DISK] Estado do MCP salvo em disco com sucesso.
2025-05-25 00:34:32 - callbacks.transformer_inputs - DEBUG - [UpdateTransformerCalc] MCP atualizado e salvo no disco.
2025-05-25 00:34:32 - callbacks.transformer_inputs - DEBUG - [UpdateTransformerCalc] Dados propagados para todos os stores alvo.
2025-05-25 12:24:09 - callbacks.temperature_rise - INFO - Registrando callbacks do módulo temperature_rise para app Simulador de Testes de Transformadores (IEC/IEEE/ABNT) EPS 1500...
2025-05-25 12:24:09 - callbacks.temperature_rise - INFO - Callbacks do módulo temperature_rise já registrados via decoradores @app.callback.
2025-05-25 12:24:09 - app - INFO - Callbacks registrados. Total: 50
2025-05-25 12:24:09 - callbacks.insulation_level_callbacks - INFO - Registrando callbacks de níveis de isolamento (APENAS OPÇÕES - V2)...
2025-05-25 12:24:09 - callbacks.insulation_level_callbacks - INFO - Callbacks de níveis de isolamento (APENAS OPÇÕES - V2) registrados.
2025-05-25 12:24:09 - callbacks.transformer_inputs - INFO - Registrando callbacks do módulo transformer_inputs para app Simulador de Testes de Transformadores (IEC/IEEE/ABNT) EPS 1500...
2025-05-25 12:24:09 - callbacks.temperature_rise - INFO - Registrando callbacks do módulo temperature_rise para app Simulador de Testes de Transformadores (IEC/IEEE/ABNT) EPS 1500...
2025-05-25 12:24:09 - callbacks.temperature_rise - INFO - Callbacks do módulo temperature_rise já registrados via decoradores @app.callback.
2025-05-25 12:24:09 - __main__ - INFO - Callbacks registrados. Total: 35
2025-05-25 12:24:09 - __main__ - INFO - Iniciando servidor Dash em http://127.0.0.1:8050/ (Debug: True)
2025-05-25 12:24:09 - dash.dash - INFO - Dash is running on http://127.0.0.1:8050/

2025-05-25 12:24:15 - callbacks.global_updates - WARNING - Dados essenciais ausentes no transformer_store_data
2025-05-25 12:24:15 - components.transformer_info_template - WARNING - transformer_data inválido ou vazio: {}
2025-05-25 12:24:15 - layouts.transformer_inputs - INFO - Criando layout Dados Básicos (v5 - Layout Geral e Pesos em 2 linhas)...
2025-05-25 12:24:15 - callbacks.global_updates - DEBUG - Painel atualizado em 2.0ms
2025-05-25 12:24:15 - layouts.transformer_inputs - INFO - Classes de tensão carregadas para dropdown: 27 opções.
2025-05-25 12:24:15 - callbacks.global_updates - WARNING - Dados essenciais ausentes no transformer_store_data
2025-05-25 12:24:15 - components.transformer_info_template - WARNING - transformer_data inválido ou vazio: {}
2025-05-25 12:24:15 - callbacks.global_updates - DEBUG - Painel atualizado em 1.0ms
2025-05-25 12:24:15 - callbacks.transformer_inputs - INFO - [LOAD TransformerInputs] Acionado por: None
2025-05-25 12:24:15 - callbacks.transformer_inputs - INFO - [LOAD TransformerInputs] Opções dos dropdowns ainda não foram populadas, aguardando...
2025-05-25 12:24:15 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_kv: 69, Tensão: 69, Path: /dados
2025-05-25 12:24:15 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT] Valores do MCP: NBI=None, SIL=None, TA=None, TI=None
2025-05-25 12:24:15 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT-NEUTRO] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_Neutro: None, Conexão: triangulo, Tensão: 69
2025-05-25 12:24:15 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT] Valores salvos: NBI=None, SIL=None, TA=None, TI=
2025-05-25 12:24:15 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_kv: 3.6, Tensão: 13.8, Path: /dados
2025-05-25 12:24:15 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT-NEUTRO] Valores do MCP: NBI=None, SIL=None
2025-05-25 12:24:15 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT] Valores do MCP: NBI=None, SIL=None, TA=None, TI=None
2025-05-25 12:24:15 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT-NEUTRO] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_Neutro: None, Conexão: triangulo, Tensão: 13.8
2025-05-25 12:24:15 - app_core.isolation_repo - WARNING - [ISOLATION] Nenhum registro encontrado para Um=69.0kV e norma=IEC/NBR. Usando valores padrão.
2025-05-25 12:24:15 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT-NEUTRO] Valores salvos: NBI=None, SIL=None
2025-05-25 12:24:15 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_kv: 24, Tensão: 13, Path: /dados
2025-05-25 12:24:15 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT] Valores salvos: NBI=None, SIL=None, TA=None, TI=/dados
2025-05-25 12:24:15 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO-NEUTRO] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_Neutro: None, Conexão: triangulo, Tensão: 13
2025-05-25 12:24:15 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT-NEUTRO] Valores do MCP: NBI=None, SIL=None
2025-05-25 12:24:15 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - AT] Opções FILTRADAS por Um=69.0 e Norma=IEC.
2025-05-25 12:24:15 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO] Valores do MCP: NBI=None, SIL=None, TA=None, TI=None
2025-05-25 12:24:15 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO-NEUTRO] Valores do MCP: NBI=None, SIL=None
2025-05-25 12:24:15 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT-NEUTRO] Valores salvos: NBI=None, SIL=None
2025-05-25 12:24:15 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - BT] Opções FILTRADAS por Um=3.6 e Norma=IEC.
2025-05-25 12:24:15 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO] Valores salvos: NBI=None, SIL=None, TA=None, TI=/dados
2025-05-25 12:24:15 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO-NEUTRO] Valores salvos: NBI=None, SIL=None
2025-05-25 12:24:15 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - TERCIARIO] Opções FILTRADAS por Um=24.0 e Norma=IEC.
2025-05-25 12:24:19 - callbacks.transformer_inputs - INFO - [LOAD TransformerInputs] Acionado por: url
2025-05-25 12:24:19 - callbacks.transformer_inputs - DEBUG - [LOAD TransformerInputs] Não estamos na página de dados (pathname=/impulso, clean_path=impulso), prevenindo atualização
2025-05-25 12:24:19 - layouts.impulse - INFO - Criando layout de impulso...
2025-05-25 12:24:19 - layouts.impulse - INFO - [Impulse] Dados do transformador obtidos do MCP: 58
2025-05-25 12:24:19 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT] Acionado por: url. Norma: IEC, Um_kv: 69, Tensão: 69, Path: /impulso
2025-05-25 12:24:19 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT] Acionado por: url. Norma: IEC, Um_kv: 3.6, Tensão: 13.8, Path: /impulso
2025-05-25 12:24:19 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT] Valores do MCP: NBI=None, SIL=None, TA=None, TI=None
2025-05-25 12:24:19 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO] Acionado por: url. Norma: IEC, Um_kv: 24, Tensão: 13, Path: /impulso
2025-05-25 12:24:19 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT] Valores do MCP: NBI=None, SIL=None, TA=None, TI=None
2025-05-25 12:24:19 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT] Valores salvos: NBI=None, SIL=None, TA=None, TI=
2025-05-25 12:24:19 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO] Valores do MCP: NBI=None, SIL=None, TA=None, TI=None
2025-05-25 12:24:19 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT-NEUTRO] Acionado por: url. Norma: IEC, Um_Neutro: None, Conexão: triangulo, Tensão: 69
2025-05-25 12:24:19 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT] Valores salvos: NBI=None, SIL=None, TA=None, TI=/impulso
2025-05-25 12:24:19 - app_core.isolation_repo - WARNING - [ISOLATION] Nenhum registro encontrado para Um=69.0kV e norma=IEC/NBR. Usando valores padrão.
2025-05-25 12:24:19 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT-NEUTRO] Acionado por: url. Norma: IEC, Um_Neutro: None, Conexão: triangulo, Tensão: 13.8
2025-05-25 12:24:19 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO] Valores salvos: NBI=None, SIL=None, TA=None, TI=/impulso
2025-05-25 12:24:19 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT-NEUTRO] Valores do MCP: NBI=None, SIL=None
2025-05-25 12:24:19 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - AT] Opções FILTRADAS por Um=69.0 e Norma=IEC.
2025-05-25 12:24:19 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - BT] Opções FILTRADAS por Um=3.6 e Norma=IEC.
2025-05-25 12:24:19 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT-NEUTRO] Valores do MCP: NBI=None, SIL=None
2025-05-25 12:24:19 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT-NEUTRO] Valores salvos: NBI=None, SIL=None
2025-05-25 12:24:19 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - TERCIARIO] Opções FILTRADAS por Um=24.0 e Norma=IEC.
2025-05-25 12:24:19 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT-NEUTRO] Valores salvos: NBI=None, SIL=None
2025-05-25 12:24:19 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO-NEUTRO] Acionado por: url. Norma: IEC, Um_Neutro: None, Conexão: triangulo, Tensão: 13
2025-05-25 12:24:19 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO-NEUTRO] Valores do MCP: NBI=None, SIL=None
2025-05-25 12:24:19 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO-NEUTRO] Valores salvos: NBI=None, SIL=None
2025-05-25 12:24:19 - callbacks.global_updates - WARNING - Dados essenciais ausentes no transformer_store_data
2025-05-25 12:24:19 - components.transformer_info_template - WARNING - transformer_data inválido ou vazio: {}
2025-05-25 12:24:19 - callbacks.global_updates - DEBUG - Painel atualizado em 0.0ms
2025-05-25 12:24:31 - layouts.history - INFO - Criando layout de Histórico de Sessões (VERSÃO REFEITA)
2025-05-25 12:24:31 - callbacks.global_updates - WARNING - Dados essenciais ausentes no transformer_store_data
2025-05-25 12:24:31 - components.transformer_info_template - WARNING - transformer_data inválido ou vazio: {}
2025-05-25 12:24:31 - callbacks.global_updates - DEBUG - Painel atualizado em 1.0ms
2025-05-25 12:24:32 - callbacks.global_updates - WARNING - Dados essenciais ausentes no transformer_store_data
2025-05-25 12:24:32 - components.transformer_info_template - WARNING - transformer_data inválido ou vazio: {}
2025-05-25 12:24:32 - callbacks.global_updates - DEBUG - Painel atualizado em 0.0ms
2025-05-25 12:24:57 - callbacks.temperature_rise - INFO - Registrando callbacks do módulo temperature_rise para app Simulador de Testes de Transformadores (IEC/IEEE/ABNT) EPS 1500...
2025-05-25 12:24:57 - callbacks.temperature_rise - INFO - Callbacks do módulo temperature_rise já registrados via decoradores @app.callback.
2025-05-25 12:24:57 - app - INFO - Callbacks registrados. Total: 50
2025-05-25 12:24:57 - callbacks.insulation_level_callbacks - INFO - Registrando callbacks de níveis de isolamento (APENAS OPÇÕES - V2)...
2025-05-25 12:24:57 - callbacks.insulation_level_callbacks - INFO - Callbacks de níveis de isolamento (APENAS OPÇÕES - V2) registrados.
2025-05-25 12:24:57 - callbacks.transformer_inputs - INFO - Registrando callbacks do módulo transformer_inputs para app Simulador de Testes de Transformadores (IEC/IEEE/ABNT) EPS 1500...
2025-05-25 12:24:57 - callbacks.temperature_rise - INFO - Registrando callbacks do módulo temperature_rise para app Simulador de Testes de Transformadores (IEC/IEEE/ABNT) EPS 1500...
2025-05-25 12:24:57 - callbacks.temperature_rise - INFO - Callbacks do módulo temperature_rise já registrados via decoradores @app.callback.
2025-05-25 12:24:57 - __main__ - INFO - Callbacks registrados. Total: 35
2025-05-25 12:24:57 - __main__ - INFO - Iniciando servidor Dash em http://127.0.0.1:8050/ (Debug: True)
2025-05-25 12:24:57 - dash.dash - INFO - Dash is running on http://127.0.0.1:8050/

2025-05-25 12:24:58 - app - ERROR - Exception on /_dash-update-component [POST]
Traceback (most recent call last):
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\dash\dash.py", line 1303, in dispatch
    cb = self.callback_map[output]
         ~~~~~~~~~~~~~~~~~^^^^^^^^
KeyError: '..nav-container-dados.style...nav-container-perdas.style...nav-container-impulso.style...nav-container-analise-dieletrica.style...nav-container-tensao-aplicada.style...nav-container-tensao-induzida.style...nav-container-curto-circuito.style...nav-container-elevacao-temperatura.style...nav-container-historico.style...nav-container-consulta-normas.style..'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\flask\app.py", line 2190, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\flask\app.py", line 1486, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\dash\dash.py", line 1368, in dispatch
    raise KeyError(msg) from missing_callback_function
KeyError: "Callback function not found for output '..nav-container-dados.style...nav-container-perdas.style...nav-container-impulso.style...nav-container-analise-dieletrica.style...nav-container-tensao-aplicada.style...nav-container-tensao-induzida.style...nav-container-curto-circuito.style...nav-container-elevacao-temperatura.style...nav-container-historico.style...nav-container-consulta-normas.style..', perhaps you forgot to prepend the '@'?"
2025-05-25 12:24:59 - callbacks.global_updates - WARNING - Dados essenciais ausentes no transformer_store_data
2025-05-25 12:24:59 - components.transformer_info_template - WARNING - transformer_data inválido ou vazio: {}
2025-05-25 12:24:59 - callbacks.global_updates - DEBUG - Painel atualizado em 1.0ms
2025-05-25 12:25:02 - callbacks.global_updates - WARNING - Dados essenciais ausentes no transformer_store_data
2025-05-25 12:25:02 - components.transformer_info_template - WARNING - transformer_data inválido ou vazio: {}
2025-05-25 12:25:02 - callbacks.global_updates - DEBUG - Painel atualizado em 7.91ms
2025-05-25 12:25:02 - callbacks.global_updates - WARNING - Dados essenciais ausentes no transformer_store_data
2025-05-25 12:25:02 - components.transformer_info_template - WARNING - transformer_data inválido ou vazio: {}
2025-05-25 12:25:02 - callbacks.global_updates - DEBUG - Painel atualizado em 0.0ms
2025-05-25 12:25:03 - layouts.temperature_rise - INFO - [Temperature Rise] Dados do transformador obtidos do MCP: 58
2025-05-25 12:25:03 - callbacks.global_updates - WARNING - Dados essenciais ausentes no transformer_store_data
2025-05-25 12:25:03 - components.transformer_info_template - WARNING - transformer_data inválido ou vazio: {}
2025-05-25 12:25:03 - callbacks.global_updates - DEBUG - Painel atualizado em 0.0ms
2025-05-25 12:25:04 - callbacks.global_updates - WARNING - Dados essenciais ausentes no transformer_store_data
2025-05-25 12:25:04 - components.transformer_info_template - WARNING - transformer_data inválido ou vazio: {}
2025-05-25 12:25:04 - callbacks.global_updates - DEBUG - Painel atualizado em 0.0ms
2025-05-25 12:25:30 - layouts.induced_voltage - INFO - [Induced Voltage] Usando dados vazios inicialmente, serão preenchidos via callback
2025-05-25 12:25:30 - callbacks.global_updates - WARNING - Dados essenciais ausentes no transformer_store_data
2025-05-25 12:25:30 - components.transformer_info_template - WARNING - transformer_data inválido ou vazio: {}
2025-05-25 12:25:30 - callbacks.global_updates - DEBUG - Painel atualizado em 0.0ms
2025-05-25 12:26:02 - callbacks.global_updates - WARNING - Dados essenciais ausentes no transformer_store_data
2025-05-25 12:26:02 - components.transformer_info_template - WARNING - transformer_data inválido ou vazio: {}
2025-05-25 12:26:02 - callbacks.global_updates - DEBUG - Painel atualizado em 0.0ms
2025-05-25 12:26:14 - utils.mcp_persistence - INFO - [ensure_mcp_data_propagation] Store de origem transformer-inputs-store É a fonte-de-verdade para campos básicos
2025-05-25 12:26:14 - app_core.transformer_mcp - DEBUG - [MCP SET] Dados definidos para store 'losses-store'.
2025-05-25 12:26:14 - utils.mcp_persistence - INFO - [ensure_mcp_data_propagation] Dados básicos propagados para losses-store
2025-05-25 12:26:14 - layouts.losses - INFO - Creating Losses layout...
2025-05-25 12:26:14 - layouts.losses - INFO - [Losses Layout] Dados do transformador obtidos do losses-store.
2025-05-25 12:26:14 - callbacks.global_updates - WARNING - Dados essenciais ausentes no transformer_store_data
2025-05-25 12:26:14 - components.transformer_info_template - WARNING - transformer_data inválido ou vazio: {}
2025-05-25 12:26:14 - callbacks.global_updates - DEBUG - Painel atualizado em 3.0ms
2025-05-25 12:26:14 - callbacks.losses - CRITICAL - [LOSSES POPULATE VAZIO] Acionado. Aba: tab-vazio, Trigger: N/A
2025-05-25 12:26:17 - layouts.impulse - INFO - Criando layout de impulso...
2025-05-25 12:26:17 - layouts.impulse - INFO - [Impulse] Dados do transformador obtidos do MCP: 58
2025-05-25 12:26:17 - callbacks.global_updates - WARNING - Dados essenciais ausentes no transformer_store_data
2025-05-25 12:26:17 - components.transformer_info_template - WARNING - transformer_data inválido ou vazio: {}
2025-05-25 12:26:17 - callbacks.global_updates - DEBUG - Painel atualizado em 4.07ms
2025-05-25 12:26:18 - layouts.dieletric_analysis - INFO - Creating Dielectric Analysis layout (from dieletric_analysis.py)...
2025-05-25 12:26:18 - app_core.standards - ERROR - Arquivo JSON 'c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\tabela.json' não encontrado.
2025-05-25 12:26:18 - app_core.standards - ERROR - Arquivo JSON 'c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\tabela.json' não encontrado.
2025-05-25 12:26:18 - app_core.standards - ERROR - Arquivo JSON 'c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\tabela.json' não encontrado.
2025-05-25 12:26:18 - app_core.standards - ERROR - Arquivo JSON 'c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\tabela.json' não encontrado.
2025-05-25 12:26:18 - app_core.standards - CRITICAL - Falha CRÍTICA ao inicializar as classes das normas: Falha ao carregar dados JSON para NBR ou IEEE.
Traceback (most recent call last):
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\app_core\standards.py", line 613, in __init__
    raise RuntimeError("Falha ao carregar dados JSON para NBR ou IEEE.")
RuntimeError: Falha ao carregar dados JSON para NBR ou IEEE.
2025-05-25 12:26:18 - layouts.dieletric_analysis - WARNING - VerificadorTransformador is invalid upon creation in create_dielectric_layout.
2025-05-25 12:26:18 - components.ui_elements - WARNING - Verificador inválido ao criar coluna dielétrica (Alta Tensão).
2025-05-25 12:26:18 - components.ui_elements - WARNING - Verificador inválido ao criar coluna dielétrica (Baixa Tensão).
2025-05-25 12:26:18 - components.ui_elements - WARNING - Verificador inválido ao criar coluna dielétrica (Terciário).
2025-05-25 12:26:18 - callbacks.global_updates - WARNING - Dados essenciais ausentes no transformer_store_data
2025-05-25 12:26:18 - components.transformer_info_template - WARNING - transformer_data inválido ou vazio: {}
2025-05-25 12:26:18 - app_core.standards - ERROR - Arquivo JSON 'c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\tabela.json' não encontrado.
2025-05-25 12:26:18 - callbacks.global_updates - DEBUG - Painel atualizado em 1.0ms
2025-05-25 12:26:18 - app_core.standards - ERROR - Arquivo JSON 'c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\tabela.json' não encontrado.
2025-05-25 12:26:18 - app_core.standards - ERROR - Arquivo JSON 'c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\tabela.json' não encontrado.
2025-05-25 12:26:18 - app_core.standards - ERROR - Arquivo JSON 'c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\tabela.json' não encontrado.
2025-05-25 12:26:18 - app_core.standards - CRITICAL - Falha CRÍTICA ao inicializar as classes das normas: Falha ao carregar dados JSON para NBR ou IEEE.
Traceback (most recent call last):
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\app_core\standards.py", line 613, in __init__
    raise RuntimeError("Falha ao carregar dados JSON para NBR ou IEEE.")
RuntimeError: Falha ao carregar dados JSON para NBR ou IEEE.
2025-05-25 12:26:18 - callbacks.dieletric_analysis - ERROR - Instância VerificadorTransformador criada, mas inválida (falha dados?).
2025-05-25 12:26:21 - layouts.applied_voltage - INFO - [Applied Voltage Layout] Creating layout...
2025-05-25 12:26:21 - callbacks.global_updates - WARNING - Dados essenciais ausentes no transformer_store_data
2025-05-25 12:26:21 - app - ERROR - Exception on /_dash-update-component [POST]
Traceback (most recent call last):
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\flask\app.py", line 2190, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\flask\app.py", line 1486, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\dash\dash.py", line 1373, in dispatch
    ctx.run(
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\dash\_callback.py", line 465, in add_context
    output_value = _invoke_callback(func, *func_args, **func_kwargs)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\dash\_callback.py", line 40, in _invoke_callback
    return func(*args, **kwargs)  # %% callback invoked %%
           ^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\callbacks\applied_voltage.py", line 821, in load_applied_voltage_inputs
    raise dash.no_update
TypeError: exceptions must derive from BaseException
2025-05-25 12:26:21 - components.transformer_info_template - WARNING - transformer_data inválido ou vazio: {}
2025-05-25 12:26:21 - callbacks.global_updates - DEBUG - Painel atualizado em 3.65ms
2025-05-25 12:26:26 - app - ERROR - Exception on /_dash-update-component [POST]
Traceback (most recent call last):
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\flask\app.py", line 2190, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\flask\app.py", line 1486, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\dash\dash.py", line 1373, in dispatch
    ctx.run(
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\dash\_callback.py", line 465, in add_context
    output_value = _invoke_callback(func, *func_args, **func_kwargs)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\dash\_callback.py", line 40, in _invoke_callback
    return func(*args, **kwargs)  # %% callback invoked %%
           ^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\callbacks\applied_voltage.py", line 784, in load_applied_voltage_inputs
    raise dash.no_update
TypeError: exceptions must derive from BaseException
2025-05-25 12:26:26 - layouts.induced_voltage - INFO - [Induced Voltage] Usando dados vazios inicialmente, serão preenchidos via callback
2025-05-25 12:26:26 - callbacks.global_updates - WARNING - Dados essenciais ausentes no transformer_store_data
2025-05-25 12:26:26 - components.transformer_info_template - WARNING - transformer_data inválido ou vazio: {}
2025-05-25 12:26:26 - callbacks.global_updates - DEBUG - Painel atualizado em 0.0ms
2025-05-25 12:26:27 - callbacks.global_updates - WARNING - Dados essenciais ausentes no transformer_store_data
2025-05-25 12:26:27 - components.transformer_info_template - WARNING - transformer_data inválido ou vazio: {}
2025-05-25 12:26:27 - callbacks.global_updates - DEBUG - Painel atualizado em 0.0ms
2025-05-25 12:26:28 - layouts.temperature_rise - INFO - [Temperature Rise] Dados do transformador obtidos do MCP: 58
2025-05-25 12:26:28 - callbacks.global_updates - WARNING - Dados essenciais ausentes no transformer_store_data
2025-05-25 12:26:28 - components.transformer_info_template - WARNING - transformer_data inválido ou vazio: {}
2025-05-25 12:26:28 - callbacks.global_updates - DEBUG - Painel atualizado em 0.0ms
2025-05-25 12:26:29 - layouts.history - INFO - Criando layout de Histórico de Sessões (VERSÃO REFEITA)
2025-05-25 12:26:29 - callbacks.global_updates - WARNING - Dados essenciais ausentes no transformer_store_data
2025-05-25 12:26:29 - components.transformer_info_template - WARNING - transformer_data inválido ou vazio: {}
2025-05-25 12:26:29 - callbacks.global_updates - DEBUG - Painel atualizado em 0.0ms
2025-05-25 12:26:29 - layouts.standards_consultation - INFO - Criando layout de consulta de normas técnicas
2025-05-25 12:26:29 - callbacks.global_updates - WARNING - Dados essenciais ausentes no transformer_store_data
2025-05-25 12:26:29 - components.transformer_info_template - WARNING - transformer_data inválido ou vazio: {}
2025-05-25 12:26:29 - callbacks.global_updates - DEBUG - Painel atualizado em 0.0ms
2025-05-25 12:26:31 - layouts.impulse - INFO - Criando layout de impulso...
2025-05-25 12:26:31 - layouts.impulse - INFO - [Impulse] Dados do transformador obtidos do MCP: 58
2025-05-25 12:26:31 - callbacks.global_updates - WARNING - Dados essenciais ausentes no transformer_store_data
2025-05-25 12:26:31 - components.transformer_info_template - WARNING - transformer_data inválido ou vazio: {}
2025-05-25 12:26:31 - callbacks.global_updates - DEBUG - Painel atualizado em 0.0ms
2025-05-25 12:26:51 - callbacks.temperature_rise - INFO - Registrando callbacks do módulo temperature_rise para app Simulador de Testes de Transformadores (IEC/IEEE/ABNT) EPS 1500...
2025-05-25 12:26:51 - callbacks.temperature_rise - INFO - Callbacks do módulo temperature_rise já registrados via decoradores @app.callback.
2025-05-25 12:26:51 - app - INFO - Callbacks registrados. Total: 50
2025-05-25 12:26:51 - callbacks.insulation_level_callbacks - INFO - Registrando callbacks de níveis de isolamento (APENAS OPÇÕES - V2)...
2025-05-25 12:26:51 - callbacks.insulation_level_callbacks - INFO - Callbacks de níveis de isolamento (APENAS OPÇÕES - V2) registrados.
2025-05-25 12:26:51 - callbacks.transformer_inputs - INFO - Registrando callbacks do módulo transformer_inputs para app Simulador de Testes de Transformadores (IEC/IEEE/ABNT) EPS 1500...
2025-05-25 12:26:51 - callbacks.temperature_rise - INFO - Registrando callbacks do módulo temperature_rise para app Simulador de Testes de Transformadores (IEC/IEEE/ABNT) EPS 1500...
2025-05-25 12:26:51 - callbacks.temperature_rise - INFO - Callbacks do módulo temperature_rise já registrados via decoradores @app.callback.
2025-05-25 12:26:51 - __main__ - INFO - Callbacks registrados. Total: 35
2025-05-25 12:26:51 - __main__ - INFO - Iniciando servidor Dash em http://127.0.0.1:8050/ (Debug: True)
2025-05-25 12:26:51 - dash.dash - INFO - Dash is running on http://127.0.0.1:8050/

2025-05-25 12:26:54 - callbacks.global_updates - WARNING - Dados essenciais ausentes no transformer_store_data
2025-05-25 12:26:54 - components.transformer_info_template - WARNING - transformer_data inválido ou vazio: {}
2025-05-25 12:26:54 - layouts.impulse - INFO - Criando layout de impulso...
2025-05-25 12:26:54 - callbacks.global_updates - DEBUG - Painel atualizado em 2.0ms
2025-05-25 12:26:54 - layouts.impulse - INFO - [Impulse] Dados do transformador obtidos do MCP: 58
2025-05-25 12:26:54 - callbacks.global_updates - WARNING - Dados essenciais ausentes no transformer_store_data
2025-05-25 12:26:54 - components.transformer_info_template - WARNING - transformer_data inválido ou vazio: {}
2025-05-25 12:26:54 - callbacks.global_updates - DEBUG - Painel atualizado em 0.0ms
2025-05-25 12:26:55 - layouts.standards_consultation - INFO - Criando layout de consulta de normas técnicas
2025-05-25 12:26:55 - callbacks.global_updates - WARNING - Dados essenciais ausentes no transformer_store_data
2025-05-25 12:26:55 - components.transformer_info_template - WARNING - transformer_data inválido ou vazio: {}
2025-05-25 12:26:55 - callbacks.global_updates - DEBUG - Painel atualizado em 0.0ms
2025-05-25 12:26:56 - layouts.history - INFO - Criando layout de Histórico de Sessões (VERSÃO REFEITA)
2025-05-25 12:26:56 - callbacks.global_updates - WARNING - Dados essenciais ausentes no transformer_store_data
2025-05-25 12:26:56 - components.transformer_info_template - WARNING - transformer_data inválido ou vazio: {}
2025-05-25 12:26:56 - callbacks.global_updates - DEBUG - Painel atualizado em 0.0ms
2025-05-25 12:26:56 - layouts.temperature_rise - INFO - [Temperature Rise] Dados do transformador obtidos do MCP: 58
2025-05-25 12:26:56 - callbacks.global_updates - WARNING - Dados essenciais ausentes no transformer_store_data
2025-05-25 12:26:56 - components.transformer_info_template - WARNING - transformer_data inválido ou vazio: {}
2025-05-25 12:26:56 - callbacks.global_updates - DEBUG - Painel atualizado em 1.0ms
2025-05-25 12:26:57 - layouts.short_circuit - INFO - [Short Circuit] Dados do transformador obtidos do MCP: 58
2025-05-25 12:26:58 - callbacks.global_updates - WARNING - Dados essenciais ausentes no transformer_store_data
2025-05-25 12:26:58 - components.transformer_info_template - WARNING - transformer_data inválido ou vazio: {}
2025-05-25 12:26:58 - callbacks.global_updates - DEBUG - Painel atualizado em 3.2ms
2025-05-25 12:27:01 - layouts.induced_voltage - INFO - [Induced Voltage] Usando dados vazios inicialmente, serão preenchidos via callback
2025-05-25 12:27:01 - components.ui_elements - WARNING - Style fornecido tanto via input_style quanto via kwargs para frequencia-teste. Usando input_style.
2025-05-25 12:27:01 - components.ui_elements - WARNING - Style fornecido tanto via input_style quanto via kwargs para capacitancia. Usando input_style.
2025-05-25 12:27:01 - callbacks.global_updates - WARNING - Dados essenciais ausentes no transformer_store_data
2025-05-25 12:27:01 - components.transformer_info_template - WARNING - transformer_data inválido ou vazio: {}
2025-05-25 12:27:01 - callbacks.global_updates - DEBUG - Painel atualizado em 0.0ms
2025-05-25 12:27:05 - layouts.applied_voltage - INFO - [Applied Voltage Layout] Creating layout...
2025-05-25 12:27:05 - callbacks.global_updates - WARNING - Dados essenciais ausentes no transformer_store_data
2025-05-25 12:27:05 - app - ERROR - Exception on /_dash-update-component [POST]
Traceback (most recent call last):
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\flask\app.py", line 2190, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\flask\app.py", line 1486, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\dash\dash.py", line 1373, in dispatch
    ctx.run(
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\dash\_callback.py", line 465, in add_context
    output_value = _invoke_callback(func, *func_args, **func_kwargs)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\dash\_callback.py", line 40, in _invoke_callback
    return func(*args, **kwargs)  # %% callback invoked %%
           ^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\callbacks\applied_voltage.py", line 821, in load_applied_voltage_inputs
    raise dash.no_update
TypeError: exceptions must derive from BaseException
2025-05-25 12:27:05 - components.transformer_info_template - WARNING - transformer_data inválido ou vazio: {}
2025-05-25 12:27:05 - callbacks.global_updates - DEBUG - Painel atualizado em 0.0ms
2025-05-25 12:27:08 - app - ERROR - Exception on /_dash-update-component [POST]
Traceback (most recent call last):
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\flask\app.py", line 2190, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\flask\app.py", line 1486, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\dash\dash.py", line 1373, in dispatch
    ctx.run(
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\dash\_callback.py", line 465, in add_context
    output_value = _invoke_callback(func, *func_args, **func_kwargs)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\dash\_callback.py", line 40, in _invoke_callback
    return func(*args, **kwargs)  # %% callback invoked %%
           ^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\callbacks\applied_voltage.py", line 784, in load_applied_voltage_inputs
    raise dash.no_update
TypeError: exceptions must derive from BaseException
2025-05-25 12:27:08 - layouts.dieletric_analysis - INFO - Creating Dielectric Analysis layout (from dieletric_analysis.py)...
2025-05-25 12:27:08 - app_core.standards - ERROR - Arquivo JSON 'c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\tabela.json' não encontrado.
2025-05-25 12:27:08 - app_core.standards - ERROR - Arquivo JSON 'c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\tabela.json' não encontrado.
2025-05-25 12:27:08 - app_core.standards - ERROR - Arquivo JSON 'c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\tabela.json' não encontrado.
2025-05-25 12:27:08 - app_core.standards - ERROR - Arquivo JSON 'c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\tabela.json' não encontrado.
2025-05-25 12:27:08 - app_core.standards - CRITICAL - Falha CRÍTICA ao inicializar as classes das normas: Falha ao carregar dados JSON para NBR ou IEEE.
Traceback (most recent call last):
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\app_core\standards.py", line 613, in __init__
    raise RuntimeError("Falha ao carregar dados JSON para NBR ou IEEE.")
RuntimeError: Falha ao carregar dados JSON para NBR ou IEEE.
2025-05-25 12:27:08 - layouts.dieletric_analysis - WARNING - VerificadorTransformador is invalid upon creation in create_dielectric_layout.
2025-05-25 12:27:08 - components.ui_elements - WARNING - Verificador inválido ao criar coluna dielétrica (Alta Tensão).
2025-05-25 12:27:08 - components.ui_elements - WARNING - Verificador inválido ao criar coluna dielétrica (Baixa Tensão).
2025-05-25 12:27:08 - components.ui_elements - WARNING - Verificador inválido ao criar coluna dielétrica (Terciário).
2025-05-25 12:27:08 - callbacks.global_updates - WARNING - Dados essenciais ausentes no transformer_store_data
2025-05-25 12:27:08 - components.transformer_info_template - WARNING - transformer_data inválido ou vazio: {}
2025-05-25 12:27:08 - callbacks.global_updates - DEBUG - Painel atualizado em 0.76ms
2025-05-25 12:27:08 - app_core.standards - ERROR - Arquivo JSON 'c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\tabela.json' não encontrado.
2025-05-25 12:27:08 - app_core.standards - ERROR - Arquivo JSON 'c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\tabela.json' não encontrado.
2025-05-25 12:27:08 - app_core.standards - ERROR - Arquivo JSON 'c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\tabela.json' não encontrado.
2025-05-25 12:27:08 - app_core.standards - ERROR - Arquivo JSON 'c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\tabela.json' não encontrado.
2025-05-25 12:27:08 - app_core.standards - CRITICAL - Falha CRÍTICA ao inicializar as classes das normas: Falha ao carregar dados JSON para NBR ou IEEE.
Traceback (most recent call last):
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\app_core\standards.py", line 613, in __init__
    raise RuntimeError("Falha ao carregar dados JSON para NBR ou IEEE.")
RuntimeError: Falha ao carregar dados JSON para NBR ou IEEE.
2025-05-25 12:27:08 - callbacks.dieletric_analysis - ERROR - Instância VerificadorTransformador criada, mas inválida (falha dados?).
2025-05-25 12:27:10 - layouts.impulse - INFO - Criando layout de impulso...
2025-05-25 12:27:10 - layouts.impulse - INFO - [Impulse] Dados do transformador obtidos do MCP: 58
2025-05-25 12:27:10 - callbacks.global_updates - WARNING - Dados essenciais ausentes no transformer_store_data
2025-05-25 12:27:10 - components.transformer_info_template - WARNING - transformer_data inválido ou vazio: {}
2025-05-25 12:27:10 - callbacks.global_updates - DEBUG - Painel atualizado em 0.0ms
2025-05-25 12:27:12 - utils.mcp_persistence - INFO - [ensure_mcp_data_propagation] Store de origem transformer-inputs-store É a fonte-de-verdade para campos básicos
2025-05-25 12:27:12 - app_core.transformer_mcp - DEBUG - [MCP SET] Dados definidos para store 'losses-store'.
2025-05-25 12:27:12 - utils.mcp_persistence - INFO - [ensure_mcp_data_propagation] Dados básicos propagados para losses-store
2025-05-25 12:27:12 - layouts.losses - INFO - Creating Losses layout...
2025-05-25 12:27:12 - layouts.losses - INFO - [Losses Layout] Dados do transformador obtidos do losses-store.
2025-05-25 12:27:12 - callbacks.global_updates - WARNING - Dados essenciais ausentes no transformer_store_data
2025-05-25 12:27:12 - components.transformer_info_template - WARNING - transformer_data inválido ou vazio: {}
2025-05-25 12:27:12 - callbacks.global_updates - DEBUG - Painel atualizado em 3.03ms
2025-05-25 12:27:12 - callbacks.losses - CRITICAL - [LOSSES POPULATE VAZIO] Acionado. Aba: tab-vazio, Trigger: N/A
2025-05-25 12:27:13 - layouts.transformer_inputs - INFO - Criando layout Dados Básicos (v5 - Layout Geral e Pesos em 2 linhas)...
2025-05-25 12:27:13 - layouts.transformer_inputs - INFO - Classes de tensão carregadas para dropdown: 27 opções.
2025-05-25 12:27:13 - callbacks.global_updates - WARNING - Dados essenciais ausentes no transformer_store_data
2025-05-25 12:27:13 - components.transformer_info_template - WARNING - transformer_data inválido ou vazio: {}
2025-05-25 12:27:13 - callbacks.global_updates - DEBUG - Painel atualizado em 1.0ms
2025-05-25 12:27:13 - callbacks.transformer_inputs - INFO - [LOAD TransformerInputs] Acionado por: None
2025-05-25 12:27:13 - callbacks.transformer_inputs - INFO - [LOAD TransformerInputs] Opções dos dropdowns ainda não foram populadas, aguardando...
2025-05-25 12:27:13 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_kv: 69, Tensão: 69, Path: /dados
2025-05-25 12:27:13 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT-NEUTRO] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_Neutro: None, Conexão: triangulo, Tensão: 69
2025-05-25 12:27:13 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT] Valores do MCP: NBI=None, SIL=None, TA=None, TI=None
2025-05-25 12:27:13 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT-NEUTRO] Valores do MCP: NBI=None, SIL=None
2025-05-25 12:27:13 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_kv: 3.6, Tensão: 13.8, Path: /dados
2025-05-25 12:27:13 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT] Valores salvos: NBI=None, SIL=None, TA=None, TI=
2025-05-25 12:27:13 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT-NEUTRO] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_Neutro: None, Conexão: triangulo, Tensão: 13.8
2025-05-25 12:27:13 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT-NEUTRO] Valores salvos: NBI=None, SIL=None
2025-05-25 12:27:13 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_kv: 24, Tensão: 13, Path: /dados
2025-05-25 12:27:13 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT] Valores do MCP: NBI=None, SIL=None, TA=None, TI=None
2025-05-25 12:27:13 - app_core.isolation_repo - WARNING - [ISOLATION] Nenhum registro encontrado para Um=69.0kV e norma=IEC/NBR. Usando valores padrão.
2025-05-25 12:27:13 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO-NEUTRO] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_Neutro: None, Conexão: triangulo, Tensão: 13
2025-05-25 12:27:13 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT-NEUTRO] Valores do MCP: NBI=None, SIL=None
2025-05-25 12:27:13 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO] Valores do MCP: NBI=None, SIL=None, TA=None, TI=None
2025-05-25 12:27:13 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT] Valores salvos: NBI=None, SIL=None, TA=None, TI=/dados
2025-05-25 12:27:13 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - AT] Opções FILTRADAS por Um=69.0 e Norma=IEC.
2025-05-25 12:27:13 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO-NEUTRO] Valores do MCP: NBI=None, SIL=None
2025-05-25 12:27:13 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT-NEUTRO] Valores salvos: NBI=None, SIL=None
2025-05-25 12:27:13 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO] Valores salvos: NBI=None, SIL=None, TA=None, TI=/dados
2025-05-25 12:27:13 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - BT] Opções FILTRADAS por Um=3.6 e Norma=IEC.
2025-05-25 12:27:13 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO-NEUTRO] Valores salvos: NBI=None, SIL=None
2025-05-25 12:27:13 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - TERCIARIO] Opções FILTRADAS por Um=24.0 e Norma=IEC.
2025-05-25 12:27:22 - callbacks.transformer_inputs - INFO - [LOAD TransformerInputs] Acionado por: url
2025-05-25 12:27:22 - callbacks.transformer_inputs - DEBUG - [LOAD TransformerInputs] Não estamos na página de dados (pathname=/perdas, clean_path=perdas), prevenindo atualização
2025-05-25 12:27:22 - utils.mcp_persistence - INFO - [ensure_mcp_data_propagation] Store de origem transformer-inputs-store É a fonte-de-verdade para campos básicos
2025-05-25 12:27:22 - app_core.transformer_mcp - DEBUG - [MCP SET] Dados definidos para store 'losses-store'.
2025-05-25 12:27:22 - utils.mcp_persistence - INFO - [ensure_mcp_data_propagation] Dados básicos propagados para losses-store
2025-05-25 12:27:22 - layouts.losses - INFO - Creating Losses layout...
2025-05-25 12:27:22 - layouts.losses - INFO - [Losses Layout] Dados do transformador obtidos do losses-store.
2025-05-25 12:27:22 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT] Acionado por: url. Norma: IEC, Um_kv: 69, Tensão: 69, Path: /perdas
2025-05-25 12:27:22 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT] Valores do MCP: NBI=None, SIL=None, TA=None, TI=None
2025-05-25 12:27:22 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT] Acionado por: url. Norma: IEC, Um_kv: 3.6, Tensão: 13.8, Path: /perdas
2025-05-25 12:27:22 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO] Acionado por: url. Norma: IEC, Um_kv: 24, Tensão: 13, Path: /perdas
2025-05-25 12:27:22 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT] Valores salvos: NBI=None, SIL=None, TA=None, TI=
2025-05-25 12:27:22 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT-NEUTRO] Acionado por: url. Norma: IEC, Um_Neutro: None, Conexão: triangulo, Tensão: 69
2025-05-25 12:27:22 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT] Valores do MCP: NBI=None, SIL=None, TA=None, TI=None
2025-05-25 12:27:22 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT-NEUTRO] Acionado por: url. Norma: IEC, Um_Neutro: None, Conexão: triangulo, Tensão: 13.8
2025-05-25 12:27:22 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO] Valores do MCP: NBI=None, SIL=None, TA=None, TI=None
2025-05-25 12:27:22 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO-NEUTRO] Acionado por: url. Norma: IEC, Um_Neutro: None, Conexão: triangulo, Tensão: 13
2025-05-25 12:27:22 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT-NEUTRO] Valores do MCP: NBI=None, SIL=None
2025-05-25 12:27:22 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT] Valores salvos: NBI=None, SIL=None, TA=None, TI=/perdas
2025-05-25 12:27:22 - app_core.isolation_repo - WARNING - [ISOLATION] Nenhum registro encontrado para Um=69.0kV e norma=IEC/NBR. Usando valores padrão.
2025-05-25 12:27:22 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT-NEUTRO] Valores do MCP: NBI=None, SIL=None
2025-05-25 12:27:22 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO] Valores salvos: NBI=None, SIL=None, TA=None, TI=/perdas
2025-05-25 12:27:22 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO-NEUTRO] Valores do MCP: NBI=None, SIL=None
2025-05-25 12:27:22 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT-NEUTRO] Valores salvos: NBI=None, SIL=None
2025-05-25 12:27:22 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - AT] Opções FILTRADAS por Um=69.0 e Norma=IEC.
2025-05-25 12:27:22 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT-NEUTRO] Valores salvos: NBI=None, SIL=None
2025-05-25 12:27:22 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - BT] Opções FILTRADAS por Um=3.6 e Norma=IEC.
2025-05-25 12:27:22 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO-NEUTRO] Valores salvos: NBI=None, SIL=None
2025-05-25 12:27:22 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - TERCIARIO] Opções FILTRADAS por Um=24.0 e Norma=IEC.
2025-05-25 12:27:22 - callbacks.global_updates - WARNING - Dados essenciais ausentes no transformer_store_data
2025-05-25 12:27:22 - components.transformer_info_template - WARNING - transformer_data inválido ou vazio: {}
2025-05-25 12:27:22 - callbacks.global_updates - DEBUG - Painel atualizado em 2.0ms
2025-05-25 12:27:22 - callbacks.losses - CRITICAL - [LOSSES POPULATE VAZIO] Acionado. Aba: tab-vazio, Trigger: N/A
2025-05-25 12:27:23 - layouts.impulse - INFO - Criando layout de impulso...
2025-05-25 12:27:23 - layouts.impulse - INFO - [Impulse] Dados do transformador obtidos do MCP: 58
2025-05-25 12:27:23 - callbacks.global_updates - WARNING - Dados essenciais ausentes no transformer_store_data
2025-05-25 12:27:23 - components.transformer_info_template - WARNING - transformer_data inválido ou vazio: {}
2025-05-25 12:27:23 - callbacks.global_updates - DEBUG - Painel atualizado em 1.01ms
2025-05-25 12:27:23 - layouts.dieletric_analysis - INFO - Creating Dielectric Analysis layout (from dieletric_analysis.py)...
2025-05-25 12:27:23 - app_core.standards - ERROR - Arquivo JSON 'c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\tabela.json' não encontrado.
2025-05-25 12:27:23 - app_core.standards - ERROR - Arquivo JSON 'c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\tabela.json' não encontrado.
2025-05-25 12:27:23 - app_core.standards - ERROR - Arquivo JSON 'c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\tabela.json' não encontrado.
2025-05-25 12:27:23 - app_core.standards - ERROR - Arquivo JSON 'c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\tabela.json' não encontrado.
2025-05-25 12:27:23 - app_core.standards - CRITICAL - Falha CRÍTICA ao inicializar as classes das normas: Falha ao carregar dados JSON para NBR ou IEEE.
Traceback (most recent call last):
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\app_core\standards.py", line 613, in __init__
    raise RuntimeError("Falha ao carregar dados JSON para NBR ou IEEE.")
RuntimeError: Falha ao carregar dados JSON para NBR ou IEEE.
2025-05-25 12:27:23 - layouts.dieletric_analysis - WARNING - VerificadorTransformador is invalid upon creation in create_dielectric_layout.
2025-05-25 12:27:23 - components.ui_elements - WARNING - Verificador inválido ao criar coluna dielétrica (Alta Tensão).
2025-05-25 12:27:23 - components.ui_elements - WARNING - Verificador inválido ao criar coluna dielétrica (Baixa Tensão).
2025-05-25 12:27:23 - components.ui_elements - WARNING - Verificador inválido ao criar coluna dielétrica (Terciário).
2025-05-25 12:27:23 - callbacks.global_updates - WARNING - Dados essenciais ausentes no transformer_store_data
2025-05-25 12:27:23 - components.transformer_info_template - WARNING - transformer_data inválido ou vazio: {}
2025-05-25 12:27:23 - app_core.standards - ERROR - Arquivo JSON 'c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\tabela.json' não encontrado.
2025-05-25 12:27:23 - callbacks.global_updates - DEBUG - Painel atualizado em 1.0ms
2025-05-25 12:27:23 - app_core.standards - ERROR - Arquivo JSON 'c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\tabela.json' não encontrado.
2025-05-25 12:27:23 - app_core.standards - ERROR - Arquivo JSON 'c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\tabela.json' não encontrado.
2025-05-25 12:27:23 - app_core.standards - ERROR - Arquivo JSON 'c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\tabela.json' não encontrado.
2025-05-25 12:27:23 - app_core.standards - CRITICAL - Falha CRÍTICA ao inicializar as classes das normas: Falha ao carregar dados JSON para NBR ou IEEE.
Traceback (most recent call last):
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\app_core\standards.py", line 613, in __init__
    raise RuntimeError("Falha ao carregar dados JSON para NBR ou IEEE.")
RuntimeError: Falha ao carregar dados JSON para NBR ou IEEE.
2025-05-25 12:27:23 - callbacks.dieletric_analysis - ERROR - Instância VerificadorTransformador criada, mas inválida (falha dados?).
2025-05-25 12:27:24 - layouts.applied_voltage - INFO - [Applied Voltage Layout] Creating layout...
2025-05-25 12:27:24 - callbacks.global_updates - WARNING - Dados essenciais ausentes no transformer_store_data
2025-05-25 12:27:24 - app - ERROR - Exception on /_dash-update-component [POST]
Traceback (most recent call last):
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\flask\app.py", line 2190, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\flask\app.py", line 1486, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\dash\dash.py", line 1373, in dispatch
    ctx.run(
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\dash\_callback.py", line 465, in add_context
    output_value = _invoke_callback(func, *func_args, **func_kwargs)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\dash\_callback.py", line 40, in _invoke_callback
    return func(*args, **kwargs)  # %% callback invoked %%
           ^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\callbacks\applied_voltage.py", line 821, in load_applied_voltage_inputs
    raise dash.no_update
TypeError: exceptions must derive from BaseException
2025-05-25 12:27:24 - components.transformer_info_template - WARNING - transformer_data inválido ou vazio: {}
2025-05-25 12:27:24 - callbacks.global_updates - DEBUG - Painel atualizado em 2.0ms
2025-05-25 12:27:24 - app - ERROR - Exception on /_dash-update-component [POST]
Traceback (most recent call last):
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\flask\app.py", line 2190, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\flask\app.py", line 1486, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\dash\dash.py", line 1373, in dispatch
    ctx.run(
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\dash\_callback.py", line 465, in add_context
    output_value = _invoke_callback(func, *func_args, **func_kwargs)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\dash\_callback.py", line 40, in _invoke_callback
    return func(*args, **kwargs)  # %% callback invoked %%
           ^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\callbacks\applied_voltage.py", line 784, in load_applied_voltage_inputs
    raise dash.no_update
TypeError: exceptions must derive from BaseException
2025-05-25 12:27:24 - layouts.induced_voltage - INFO - [Induced Voltage] Usando dados vazios inicialmente, serão preenchidos via callback
2025-05-25 12:27:24 - components.ui_elements - WARNING - Style fornecido tanto via input_style quanto via kwargs para frequencia-teste. Usando input_style.
2025-05-25 12:27:24 - components.ui_elements - WARNING - Style fornecido tanto via input_style quanto via kwargs para capacitancia. Usando input_style.
2025-05-25 12:27:24 - callbacks.global_updates - WARNING - Dados essenciais ausentes no transformer_store_data
2025-05-25 12:27:24 - components.transformer_info_template - WARNING - transformer_data inválido ou vazio: {}
2025-05-25 12:27:24 - callbacks.global_updates - DEBUG - Painel atualizado em 1.0ms
2025-05-25 12:27:25 - layouts.short_circuit - INFO - [Short Circuit] Dados do transformador obtidos do MCP: 58
2025-05-25 12:27:25 - callbacks.global_updates - WARNING - Dados essenciais ausentes no transformer_store_data
2025-05-25 12:27:25 - components.transformer_info_template - WARNING - transformer_data inválido ou vazio: {}
2025-05-25 12:27:25 - callbacks.global_updates - DEBUG - Painel atualizado em 3.0ms
2025-05-25 12:27:26 - layouts.temperature_rise - INFO - [Temperature Rise] Dados do transformador obtidos do MCP: 58
2025-05-25 12:27:26 - callbacks.global_updates - WARNING - Dados essenciais ausentes no transformer_store_data
2025-05-25 12:27:26 - components.transformer_info_template - WARNING - transformer_data inválido ou vazio: {}
2025-05-25 12:27:26 - callbacks.global_updates - DEBUG - Painel atualizado em 0.0ms
2025-05-25 12:27:26 - layouts.history - INFO - Criando layout de Histórico de Sessões (VERSÃO REFEITA)
2025-05-25 12:27:26 - callbacks.global_updates - WARNING - Dados essenciais ausentes no transformer_store_data
2025-05-25 12:27:26 - components.transformer_info_template - WARNING - transformer_data inválido ou vazio: {}
2025-05-25 12:27:26 - callbacks.global_updates - DEBUG - Painel atualizado em 0.0ms
2025-05-25 12:27:27 - layouts.standards_consultation - INFO - Criando layout de consulta de normas técnicas
2025-05-25 12:27:27 - callbacks.global_updates - WARNING - Dados essenciais ausentes no transformer_store_data
2025-05-25 12:27:27 - components.transformer_info_template - WARNING - transformer_data inválido ou vazio: {}
2025-05-25 12:27:27 - callbacks.global_updates - DEBUG - Painel atualizado em 1.0ms
2025-05-25 12:28:37 - utils.mcp_persistence - INFO - [ensure_mcp_data_propagation] Store de origem transformer-inputs-store É a fonte-de-verdade para campos básicos
2025-05-25 12:28:37 - app_core.transformer_mcp - DEBUG - [MCP SET] Dados definidos para store 'losses-store'.
2025-05-25 12:28:37 - utils.mcp_persistence - INFO - [ensure_mcp_data_propagation] Dados básicos propagados para losses-store
2025-05-25 12:28:37 - layouts.losses - INFO - Creating Losses layout...
2025-05-25 12:28:37 - layouts.losses - INFO - [Losses Layout] Dados do transformador obtidos do losses-store.
2025-05-25 12:28:37 - callbacks.global_updates - WARNING - Dados essenciais ausentes no transformer_store_data
2025-05-25 12:28:37 - components.transformer_info_template - WARNING - transformer_data inválido ou vazio: {}
2025-05-25 12:28:37 - callbacks.global_updates - DEBUG - Painel atualizado em 2.15ms
2025-05-25 12:28:37 - callbacks.losses - CRITICAL - [LOSSES POPULATE VAZIO] Acionado. Aba: tab-vazio, Trigger: N/A
2025-05-25 12:28:38 - layouts.short_circuit - INFO - [Short Circuit] Dados do transformador obtidos do MCP: 58
2025-05-25 12:28:38 - callbacks.global_updates - WARNING - Dados essenciais ausentes no transformer_store_data
2025-05-25 12:28:38 - components.transformer_info_template - WARNING - transformer_data inválido ou vazio: {}
2025-05-25 12:28:38 - callbacks.global_updates - DEBUG - Painel atualizado em 15.63ms
2025-05-25 12:28:38 - layouts.transformer_inputs - INFO - Criando layout Dados Básicos (v5 - Layout Geral e Pesos em 2 linhas)...
2025-05-25 12:28:38 - layouts.transformer_inputs - INFO - Classes de tensão carregadas para dropdown: 27 opções.
2025-05-25 12:28:38 - callbacks.global_updates - WARNING - Dados essenciais ausentes no transformer_store_data
2025-05-25 12:28:38 - components.transformer_info_template - WARNING - transformer_data inválido ou vazio: {}
2025-05-25 12:28:38 - callbacks.global_updates - DEBUG - Painel atualizado em 1.0ms
2025-05-25 12:28:38 - callbacks.transformer_inputs - INFO - [LOAD TransformerInputs] Acionado por: None
2025-05-25 12:28:38 - callbacks.transformer_inputs - INFO - [LOAD TransformerInputs] Opções dos dropdowns ainda não foram populadas, aguardando...
2025-05-25 12:28:38 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_kv: 69, Tensão: 69, Path: /dados
2025-05-25 12:28:38 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT] Valores do MCP: NBI=None, SIL=None, TA=None, TI=None
2025-05-25 12:28:38 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT-NEUTRO] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_Neutro: None, Conexão: triangulo, Tensão: 69
2025-05-25 12:28:38 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT] Valores salvos: NBI=None, SIL=None, TA=None, TI=
2025-05-25 12:28:38 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_kv: 3.6, Tensão: 13.8, Path: /dados
2025-05-25 12:28:38 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT-NEUTRO] Valores do MCP: NBI=None, SIL=None
2025-05-25 12:28:38 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT-NEUTRO] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_Neutro: None, Conexão: triangulo, Tensão: 13.8
2025-05-25 12:28:38 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT] Valores do MCP: NBI=None, SIL=None, TA=None, TI=None
2025-05-25 12:28:38 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT-NEUTRO] Valores salvos: NBI=None, SIL=None
2025-05-25 12:28:38 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_kv: 24, Tensão: 13, Path: /dados
2025-05-25 12:28:38 - app_core.isolation_repo - WARNING - [ISOLATION] Nenhum registro encontrado para Um=69.0kV e norma=IEC/NBR. Usando valores padrão.
2025-05-25 12:28:38 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO-NEUTRO] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_Neutro: None, Conexão: triangulo, Tensão: 13
2025-05-25 12:28:38 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT-NEUTRO] Valores do MCP: NBI=None, SIL=None
2025-05-25 12:28:38 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT] Valores salvos: NBI=None, SIL=None, TA=None, TI=/dados
2025-05-25 12:28:38 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO] Valores do MCP: NBI=None, SIL=None, TA=None, TI=None
2025-05-25 12:28:38 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - AT] Opções FILTRADAS por Um=69.0 e Norma=IEC.
2025-05-25 12:28:38 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO-NEUTRO] Valores do MCP: NBI=None, SIL=None
2025-05-25 12:28:38 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT-NEUTRO] Valores salvos: NBI=None, SIL=None
2025-05-25 12:28:38 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO] Valores salvos: NBI=None, SIL=None, TA=None, TI=/dados
2025-05-25 12:28:38 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - BT] Opções FILTRADAS por Um=3.6 e Norma=IEC.
2025-05-25 12:28:38 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO-NEUTRO] Valores salvos: NBI=None, SIL=None
2025-05-25 12:28:38 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - TERCIARIO] Opções FILTRADAS por Um=24.0 e Norma=IEC.
2025-05-25 12:31:10 - callbacks.transformer_inputs - INFO - [LOAD TransformerInputs] Acionado por: url
2025-05-25 12:31:10 - callbacks.transformer_inputs - DEBUG - [LOAD TransformerInputs] Não estamos na página de dados (pathname=/perdas, clean_path=perdas), prevenindo atualização
2025-05-25 12:31:10 - utils.mcp_persistence - INFO - [ensure_mcp_data_propagation] Store de origem transformer-inputs-store É a fonte-de-verdade para campos básicos
2025-05-25 12:31:10 - app_core.transformer_mcp - DEBUG - [MCP SET] Dados definidos para store 'losses-store'.
2025-05-25 12:31:10 - utils.mcp_persistence - INFO - [ensure_mcp_data_propagation] Dados básicos propagados para losses-store
2025-05-25 12:31:10 - layouts.losses - INFO - Creating Losses layout...
2025-05-25 12:31:10 - layouts.losses - INFO - [Losses Layout] Dados do transformador obtidos do losses-store.
2025-05-25 12:31:10 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT] Acionado por: url. Norma: IEC, Um_kv: 69, Tensão: 69, Path: /perdas
2025-05-25 12:31:10 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT] Valores do MCP: NBI=None, SIL=None, TA=None, TI=None
2025-05-25 12:31:10 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT] Acionado por: url. Norma: IEC, Um_kv: 3.6, Tensão: 13.8, Path: /perdas
2025-05-25 12:31:10 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT] Valores salvos: NBI=None, SIL=None, TA=None, TI=
2025-05-25 12:31:10 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO] Acionado por: url. Norma: IEC, Um_kv: 24, Tensão: 13, Path: /perdas
2025-05-25 12:31:10 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT] Valores do MCP: NBI=None, SIL=None, TA=None, TI=None
2025-05-25 12:31:10 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT-NEUTRO] Acionado por: url. Norma: IEC, Um_Neutro: None, Conexão: triangulo, Tensão: 69
2025-05-25 12:31:10 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO] Valores do MCP: NBI=None, SIL=None, TA=None, TI=None
2025-05-25 12:31:10 - app_core.isolation_repo - WARNING - [ISOLATION] Nenhum registro encontrado para Um=69.0kV e norma=IEC/NBR. Usando valores padrão.
2025-05-25 12:31:10 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT] Valores salvos: NBI=None, SIL=None, TA=None, TI=/perdas
2025-05-25 12:31:10 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT-NEUTRO] Acionado por: url. Norma: IEC, Um_Neutro: None, Conexão: triangulo, Tensão: 13.8
2025-05-25 12:31:10 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT-NEUTRO] Valores do MCP: NBI=None, SIL=None
2025-05-25 12:31:10 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO] Valores salvos: NBI=None, SIL=None, TA=None, TI=/perdas
2025-05-25 12:31:10 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO-NEUTRO] Acionado por: url. Norma: IEC, Um_Neutro: None, Conexão: triangulo, Tensão: 13
2025-05-25 12:31:10 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - AT] Opções FILTRADAS por Um=69.0 e Norma=IEC.
2025-05-25 12:31:10 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT-NEUTRO] Valores do MCP: NBI=None, SIL=None
2025-05-25 12:31:10 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - BT] Opções FILTRADAS por Um=3.6 e Norma=IEC.
2025-05-25 12:31:10 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT-NEUTRO] Valores salvos: NBI=None, SIL=None
2025-05-25 12:31:10 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO-NEUTRO] Valores do MCP: NBI=None, SIL=None
2025-05-25 12:31:10 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - TERCIARIO] Opções FILTRADAS por Um=24.0 e Norma=IEC.
2025-05-25 12:31:10 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT-NEUTRO] Valores salvos: NBI=None, SIL=None
2025-05-25 12:31:10 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO-NEUTRO] Valores salvos: NBI=None, SIL=None
2025-05-25 12:31:10 - callbacks.global_updates - WARNING - Dados essenciais ausentes no transformer_store_data
2025-05-25 12:31:10 - components.transformer_info_template - WARNING - transformer_data inválido ou vazio: {}
2025-05-25 12:31:10 - callbacks.global_updates - DEBUG - Painel atualizado em 0.91ms
2025-05-25 12:31:10 - callbacks.losses - CRITICAL - [LOSSES POPULATE VAZIO] Acionado. Aba: tab-vazio, Trigger: N/A
2025-05-25 12:31:12 - layouts.transformer_inputs - INFO - Criando layout Dados Básicos (v5 - Layout Geral e Pesos em 2 linhas)...
2025-05-25 12:31:12 - layouts.transformer_inputs - INFO - Classes de tensão carregadas para dropdown: 27 opções.
2025-05-25 12:31:12 - callbacks.global_updates - WARNING - Dados essenciais ausentes no transformer_store_data
2025-05-25 12:31:12 - components.transformer_info_template - WARNING - transformer_data inválido ou vazio: {}
2025-05-25 12:31:12 - callbacks.transformer_inputs - INFO - [LOAD TransformerInputs] Acionado por: None
2025-05-25 12:31:12 - callbacks.global_updates - DEBUG - Painel atualizado em 1.0ms
2025-05-25 12:31:12 - callbacks.transformer_inputs - INFO - [LOAD TransformerInputs] Opções dos dropdowns ainda não foram populadas, aguardando...
2025-05-25 12:31:12 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_kv: 69, Tensão: 69, Path: /dados
2025-05-25 12:31:12 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT-NEUTRO] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_Neutro: None, Conexão: triangulo, Tensão: 69
2025-05-25 12:31:12 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_kv: 3.6, Tensão: 13.8, Path: /dados
2025-05-25 12:31:12 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT] Valores do MCP: NBI=None, SIL=None, TA=None, TI=None
2025-05-25 12:31:12 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT-NEUTRO] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_Neutro: None, Conexão: triangulo, Tensão: 13.8
2025-05-25 12:31:12 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT-NEUTRO] Valores do MCP: NBI=None, SIL=None
2025-05-25 12:31:12 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT] Valores do MCP: NBI=None, SIL=None, TA=None, TI=None
2025-05-25 12:31:12 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_kv: 24, Tensão: 13, Path: /dados
2025-05-25 12:31:12 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT] Valores salvos: NBI=None, SIL=None, TA=None, TI=
2025-05-25 12:31:12 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO-NEUTRO] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_Neutro: None, Conexão: triangulo, Tensão: 13
2025-05-25 12:31:12 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT-NEUTRO] Valores do MCP: NBI=None, SIL=None
2025-05-25 12:31:12 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT-NEUTRO] Valores salvos: NBI=None, SIL=None
2025-05-25 12:31:12 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT] Valores salvos: NBI=None, SIL=None, TA=None, TI=/dados
2025-05-25 12:31:12 - app_core.isolation_repo - WARNING - [ISOLATION] Nenhum registro encontrado para Um=69.0kV e norma=IEC/NBR. Usando valores padrão.
2025-05-25 12:31:12 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO] Valores do MCP: NBI=None, SIL=None, TA=None, TI=None
2025-05-25 12:31:12 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO-NEUTRO] Valores do MCP: NBI=None, SIL=None
2025-05-25 12:31:12 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT-NEUTRO] Valores salvos: NBI=None, SIL=None
2025-05-25 12:31:12 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - AT] Opções FILTRADAS por Um=69.0 e Norma=IEC.
2025-05-25 12:31:12 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO] Valores salvos: NBI=None, SIL=None, TA=None, TI=/dados
2025-05-25 12:31:12 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO-NEUTRO] Valores salvos: NBI=None, SIL=None
2025-05-25 12:31:12 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - BT] Opções FILTRADAS por Um=3.6 e Norma=IEC.
2025-05-25 12:31:12 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - TERCIARIO] Opções FILTRADAS por Um=24.0 e Norma=IEC.
2025-05-25 12:33:00 - callbacks.temperature_rise - INFO - Registrando callbacks do módulo temperature_rise para app Simulador de Testes de Transformadores (IEC/IEEE/ABNT) EPS 1500...
2025-05-25 12:33:00 - callbacks.temperature_rise - INFO - Callbacks do módulo temperature_rise já registrados via decoradores @app.callback.
2025-05-25 12:33:00 - app - INFO - Callbacks registrados. Total: 50
2025-05-25 12:33:00 - callbacks.insulation_level_callbacks - INFO - Registrando callbacks de níveis de isolamento (APENAS OPÇÕES - V2)...
2025-05-25 12:33:00 - callbacks.insulation_level_callbacks - INFO - Callbacks de níveis de isolamento (APENAS OPÇÕES - V2) registrados.
2025-05-25 12:33:00 - callbacks.transformer_inputs - INFO - Registrando callbacks do módulo transformer_inputs para app Simulador de Testes de Transformadores (IEC/IEEE/ABNT) EPS 1500...
2025-05-25 12:33:00 - callbacks.temperature_rise - INFO - Registrando callbacks do módulo temperature_rise para app Simulador de Testes de Transformadores (IEC/IEEE/ABNT) EPS 1500...
2025-05-25 12:33:00 - callbacks.temperature_rise - INFO - Callbacks do módulo temperature_rise já registrados via decoradores @app.callback.
2025-05-25 12:33:00 - __main__ - INFO - Callbacks registrados. Total: 35
2025-05-25 12:33:00 - __main__ - INFO - Iniciando servidor Dash em http://127.0.0.1:8050/ (Debug: True)
2025-05-25 12:33:00 - dash.dash - INFO - Dash is running on http://127.0.0.1:8050/

2025-05-25 12:33:03 - callbacks.global_updates - WARNING - Dados essenciais ausentes no transformer_store_data
2025-05-25 12:33:03 - components.transformer_info_template - WARNING - transformer_data inválido ou vazio: {}
2025-05-25 12:33:03 - layouts.transformer_inputs - INFO - Criando layout Dados Básicos (v5 - Layout Geral e Pesos em 2 linhas)...
2025-05-25 12:33:03 - callbacks.global_updates - DEBUG - Painel atualizado em 1.0ms
2025-05-25 12:33:03 - layouts.transformer_inputs - INFO - Classes de tensão carregadas para dropdown: 27 opções.
2025-05-25 12:33:03 - callbacks.global_updates - WARNING - Dados essenciais ausentes no transformer_store_data
2025-05-25 12:33:03 - components.transformer_info_template - WARNING - transformer_data inválido ou vazio: {}
2025-05-25 12:33:03 - callbacks.global_updates - DEBUG - Painel atualizado em 0.99ms
2025-05-25 12:33:03 - callbacks.transformer_inputs - INFO - [LOAD TransformerInputs] Acionado por: None
2025-05-25 12:33:03 - callbacks.transformer_inputs - INFO - [LOAD TransformerInputs] Opções dos dropdowns ainda não foram populadas, aguardando...
2025-05-25 12:33:03 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_kv: 69, Tensão: 69, Path: /dados
2025-05-25 12:33:03 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT-NEUTRO] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_Neutro: None, Conexão: triangulo, Tensão: 69
2025-05-25 12:33:03 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT] Valores do MCP: NBI=None, SIL=None, TA=None, TI=None
2025-05-25 12:33:03 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT-NEUTRO] Valores do MCP: NBI=None, SIL=None
2025-05-25 12:33:03 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_kv: 3.6, Tensão: 13.8, Path: /dados
2025-05-25 12:33:03 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT-NEUTRO] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_Neutro: None, Conexão: triangulo, Tensão: 13.8
2025-05-25 12:33:03 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT] Valores salvos: NBI=None, SIL=None, TA=None, TI=
2025-05-25 12:33:03 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT-NEUTRO] Valores salvos: NBI=None, SIL=None
2025-05-25 12:33:03 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT] Valores do MCP: NBI=None, SIL=None, TA=None, TI=None
2025-05-25 12:33:03 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_kv: 24, Tensão: 13, Path: /dados
2025-05-25 12:33:03 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO-NEUTRO] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_Neutro: None, Conexão: triangulo, Tensão: 13
2025-05-25 12:33:03 - app_core.isolation_repo - WARNING - [ISOLATION] Nenhum registro encontrado para Um=69.0kV e norma=IEC/NBR. Usando valores padrão.
2025-05-25 12:33:03 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT-NEUTRO] Valores do MCP: NBI=None, SIL=None
2025-05-25 12:33:03 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT] Valores salvos: NBI=None, SIL=None, TA=None, TI=/dados
2025-05-25 12:33:03 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO] Valores do MCP: NBI=None, SIL=None, TA=None, TI=None
2025-05-25 12:33:03 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO-NEUTRO] Valores do MCP: NBI=None, SIL=None
2025-05-25 12:33:03 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - AT] Opções FILTRADAS por Um=69.0 e Norma=IEC.
2025-05-25 12:33:03 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT-NEUTRO] Valores salvos: NBI=None, SIL=None
2025-05-25 12:33:03 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO] Valores salvos: NBI=None, SIL=None, TA=None, TI=/dados
2025-05-25 12:33:03 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO-NEUTRO] Valores salvos: NBI=None, SIL=None
2025-05-25 12:33:03 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - BT] Opções FILTRADAS por Um=3.6 e Norma=IEC.
2025-05-25 12:33:03 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - TERCIARIO] Opções FILTRADAS por Um=24.0 e Norma=IEC.
2025-05-25 12:39:33 - callbacks.temperature_rise - INFO - Registrando callbacks do módulo temperature_rise para app Simulador de Testes de Transformadores (IEC/IEEE/ABNT) EPS 1500...
2025-05-25 12:39:33 - callbacks.temperature_rise - INFO - Callbacks do módulo temperature_rise já registrados via decoradores @app.callback.
2025-05-25 12:39:33 - app - INFO - Callbacks registrados. Total: 50
2025-05-25 12:39:33 - callbacks.insulation_level_callbacks - INFO - Registrando callbacks de níveis de isolamento (APENAS OPÇÕES - V2)...
2025-05-25 12:39:33 - callbacks.insulation_level_callbacks - INFO - Callbacks de níveis de isolamento (APENAS OPÇÕES - V2) registrados.
2025-05-25 12:39:33 - callbacks.transformer_inputs - INFO - Registrando callbacks do módulo transformer_inputs para app Simulador de Testes de Transformadores (IEC/IEEE/ABNT) EPS 1500...
2025-05-25 12:39:33 - callbacks.temperature_rise - INFO - Registrando callbacks do módulo temperature_rise para app Simulador de Testes de Transformadores (IEC/IEEE/ABNT) EPS 1500...
2025-05-25 12:39:33 - callbacks.temperature_rise - INFO - Callbacks do módulo temperature_rise já registrados via decoradores @app.callback.
2025-05-25 12:39:33 - __main__ - INFO - Callbacks registrados. Total: 35
2025-05-25 12:39:33 - __main__ - INFO - Iniciando servidor Dash em http://127.0.0.1:8050/ (Debug: True)
2025-05-25 12:39:33 - dash.dash - INFO - Dash is running on http://127.0.0.1:8050/

2025-05-25 12:39:34 - callbacks.global_updates - WARNING - Dados essenciais ausentes no transformer_store_data
2025-05-25 12:39:34 - components.transformer_info_template - WARNING - transformer_data inválido ou vazio: {}
2025-05-25 12:39:34 - callbacks.global_updates - DEBUG - Painel atualizado em 1.04ms
2025-05-25 12:39:34 - layouts.transformer_inputs - INFO - Criando layout Dados Básicos (v5 - Layout Geral e Pesos em 2 linhas)...
2025-05-25 12:39:34 - layouts.transformer_inputs - INFO - Classes de tensão carregadas para dropdown: 27 opções.
2025-05-25 12:39:34 - callbacks.global_updates - WARNING - Dados essenciais ausentes no transformer_store_data
2025-05-25 12:39:34 - components.transformer_info_template - WARNING - transformer_data inválido ou vazio: {}
2025-05-25 12:39:34 - callbacks.global_updates - DEBUG - Painel atualizado em 1.0ms
2025-05-25 12:39:34 - callbacks.transformer_inputs - INFO - [LOAD TransformerInputs] Acionado por: None
2025-05-25 12:39:34 - callbacks.transformer_inputs - INFO - [LOAD TransformerInputs] Opções dos dropdowns ainda não foram populadas, aguardando...
2025-05-25 12:39:35 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_kv: 69, Tensão: 69, Path: /dados
2025-05-25 12:39:35 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT] Valores do MCP: NBI=None, SIL=None, TA=None, TI=None
2025-05-25 12:39:35 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT-NEUTRO] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_Neutro: None, Conexão: triangulo, Tensão: 69
2025-05-25 12:39:35 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT] Valores salvos: NBI=None, SIL=None, TA=None, TI=
2025-05-25 12:39:35 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_kv: 3.6, Tensão: 13.8, Path: /dados
2025-05-25 12:39:35 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT-NEUTRO] Valores do MCP: NBI=None, SIL=None
2025-05-25 12:39:35 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT] Valores do MCP: NBI=None, SIL=None, TA=None, TI=None
2025-05-25 12:39:35 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT-NEUTRO] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_Neutro: None, Conexão: triangulo, Tensão: 13.8
2025-05-25 12:39:35 - app_core.isolation_repo - WARNING - [ISOLATION] Nenhum registro encontrado para Um=69.0kV e norma=IEC/NBR. Usando valores padrão.
2025-05-25 12:39:35 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT-NEUTRO] Valores salvos: NBI=None, SIL=None
2025-05-25 12:39:35 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT] Valores salvos: NBI=None, SIL=None, TA=None, TI=/dados
2025-05-25 12:39:35 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_kv: 24, Tensão: 13, Path: /dados
2025-05-25 12:39:35 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT-NEUTRO] Valores do MCP: NBI=None, SIL=None
2025-05-25 12:39:35 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO-NEUTRO] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_Neutro: None, Conexão: triangulo, Tensão: 13
2025-05-25 12:39:35 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - AT] Opções FILTRADAS por Um=69.0 e Norma=IEC.
2025-05-25 12:39:35 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO] Valores do MCP: NBI=None, SIL=None, TA=None, TI=None
2025-05-25 12:39:35 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - BT] Opções FILTRADAS por Um=3.6 e Norma=IEC.
2025-05-25 12:39:35 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT-NEUTRO] Valores salvos: NBI=None, SIL=None
2025-05-25 12:39:35 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO-NEUTRO] Valores do MCP: NBI=None, SIL=None
2025-05-25 12:39:35 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO] Valores salvos: NBI=None, SIL=None, TA=None, TI=/dados
2025-05-25 12:39:35 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO-NEUTRO] Valores salvos: NBI=None, SIL=None
2025-05-25 12:39:35 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - TERCIARIO] Opções FILTRADAS por Um=24.0 e Norma=IEC.
2025-05-25 12:39:35 - callbacks.global_updates - WARNING - Dados essenciais ausentes no transformer_store_data
2025-05-25 12:39:35 - components.transformer_info_template - WARNING - transformer_data inválido ou vazio: {}
2025-05-25 12:39:35 - layouts.transformer_inputs - INFO - Criando layout Dados Básicos (v5 - Layout Geral e Pesos em 2 linhas)...
2025-05-25 12:39:35 - callbacks.global_updates - DEBUG - Painel atualizado em 7.04ms
2025-05-25 12:39:35 - layouts.transformer_inputs - INFO - Classes de tensão carregadas para dropdown: 27 opções.
2025-05-25 12:39:35 - callbacks.global_updates - WARNING - Dados essenciais ausentes no transformer_store_data
2025-05-25 12:39:35 - components.transformer_info_template - WARNING - transformer_data inválido ou vazio: {}
2025-05-25 12:39:35 - callbacks.global_updates - DEBUG - Painel atualizado em 0.0ms
2025-05-25 12:39:35 - callbacks.transformer_inputs - INFO - [LOAD TransformerInputs] Acionado por: None
2025-05-25 12:39:35 - callbacks.transformer_inputs - INFO - [LOAD TransformerInputs] Opções dos dropdowns ainda não foram populadas, aguardando...
2025-05-25 12:39:36 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_kv: 69, Tensão: 69, Path: /dados
2025-05-25 12:39:36 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT-NEUTRO] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_Neutro: None, Conexão: triangulo, Tensão: 69
2025-05-25 12:39:36 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT] Valores do MCP: NBI=None, SIL=None, TA=None, TI=None
2025-05-25 12:39:36 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_kv: 3.6, Tensão: 13.8, Path: /dados
2025-05-25 12:39:36 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT-NEUTRO] Valores do MCP: NBI=None, SIL=None
2025-05-25 12:39:36 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT] Valores salvos: NBI=None, SIL=None, TA=None, TI=
2025-05-25 12:39:36 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT-NEUTRO] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_Neutro: None, Conexão: triangulo, Tensão: 13.8
2025-05-25 12:39:36 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT] Valores do MCP: NBI=None, SIL=None, TA=None, TI=None
2025-05-25 12:39:36 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_kv: 24, Tensão: 13, Path: /dados
2025-05-25 12:39:36 - app_core.isolation_repo - WARNING - [ISOLATION] Nenhum registro encontrado para Um=69.0kV e norma=IEC/NBR. Usando valores padrão.
2025-05-25 12:39:36 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT-NEUTRO] Valores salvos: NBI=None, SIL=None
2025-05-25 12:39:36 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO-NEUTRO] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_Neutro: None, Conexão: triangulo, Tensão: 13
2025-05-25 12:39:36 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT-NEUTRO] Valores do MCP: NBI=None, SIL=None
2025-05-25 12:39:36 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT] Valores salvos: NBI=None, SIL=None, TA=None, TI=/dados
2025-05-25 12:39:36 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO] Valores do MCP: NBI=None, SIL=None, TA=None, TI=None
2025-05-25 12:39:36 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - AT] Opções FILTRADAS por Um=69.0 e Norma=IEC.
2025-05-25 12:39:36 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO-NEUTRO] Valores do MCP: NBI=None, SIL=None
2025-05-25 12:39:36 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT-NEUTRO] Valores salvos: NBI=None, SIL=None
2025-05-25 12:39:36 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO] Valores salvos: NBI=None, SIL=None, TA=None, TI=/dados
2025-05-25 12:39:36 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO-NEUTRO] Valores salvos: NBI=None, SIL=None
2025-05-25 12:39:36 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - BT] Opções FILTRADAS por Um=3.6 e Norma=IEC.
2025-05-25 12:39:36 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - TERCIARIO] Opções FILTRADAS por Um=24.0 e Norma=IEC.
2025-05-25 12:39:37 - callbacks.transformer_inputs - INFO - [LOAD TransformerInputs] Acionado por: url
2025-05-25 12:39:37 - callbacks.transformer_inputs - DEBUG - [LOAD TransformerInputs] Não estamos na página de dados (pathname=/perdas, clean_path=perdas), prevenindo atualização
2025-05-25 12:39:37 - utils.mcp_persistence - INFO - [ensure_mcp_data_propagation] Store de origem transformer-inputs-store É a fonte-de-verdade para campos básicos
2025-05-25 12:39:37 - app_core.transformer_mcp - DEBUG - [MCP SET] Dados definidos para store 'losses-store'.
2025-05-25 12:39:37 - utils.mcp_persistence - INFO - [ensure_mcp_data_propagation] Dados básicos propagados para losses-store
2025-05-25 12:39:37 - layouts.losses - INFO - Creating Losses layout...
2025-05-25 12:39:37 - layouts.losses - INFO - [Losses Layout] Dados do transformador obtidos do losses-store.
2025-05-25 12:39:37 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT] Acionado por: url. Norma: IEC, Um_kv: 69, Tensão: 69, Path: /perdas
2025-05-25 12:39:37 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT] Valores do MCP: NBI=None, SIL=None, TA=None, TI=None
2025-05-25 12:39:37 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT] Acionado por: url. Norma: IEC, Um_kv: 3.6, Tensão: 13.8, Path: /perdas
2025-05-25 12:39:37 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT] Valores salvos: NBI=None, SIL=None, TA=None, TI=
2025-05-25 12:39:37 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO] Acionado por: url. Norma: IEC, Um_kv: 24, Tensão: 13, Path: /perdas
2025-05-25 12:39:37 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT] Valores do MCP: NBI=None, SIL=None, TA=None, TI=None
2025-05-25 12:39:37 - app_core.isolation_repo - WARNING - [ISOLATION] Nenhum registro encontrado para Um=69.0kV e norma=IEC/NBR. Usando valores padrão.
2025-05-25 12:39:37 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT-NEUTRO] Acionado por: url. Norma: IEC, Um_Neutro: None, Conexão: triangulo, Tensão: 69
2025-05-25 12:39:37 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO] Valores do MCP: NBI=None, SIL=None, TA=None, TI=None
2025-05-25 12:39:37 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT-NEUTRO] Acionado por: url. Norma: IEC, Um_Neutro: None, Conexão: triangulo, Tensão: 13.8
2025-05-25 12:39:37 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT] Valores salvos: NBI=None, SIL=None, TA=None, TI=/perdas
2025-05-25 12:39:37 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - AT] Opções FILTRADAS por Um=69.0 e Norma=IEC.
2025-05-25 12:39:37 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO-NEUTRO] Acionado por: url. Norma: IEC, Um_Neutro: None, Conexão: triangulo, Tensão: 13
2025-05-25 12:39:37 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT-NEUTRO] Valores do MCP: NBI=None, SIL=None
2025-05-25 12:39:37 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO] Valores salvos: NBI=None, SIL=None, TA=None, TI=/perdas
2025-05-25 12:39:37 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT-NEUTRO] Valores do MCP: NBI=None, SIL=None
2025-05-25 12:39:37 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO-NEUTRO] Valores do MCP: NBI=None, SIL=None
2025-05-25 12:39:37 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT-NEUTRO] Valores salvos: NBI=None, SIL=None
2025-05-25 12:39:37 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - BT] Opções FILTRADAS por Um=3.6 e Norma=IEC.
2025-05-25 12:39:37 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT-NEUTRO] Valores salvos: NBI=None, SIL=None
2025-05-25 12:39:37 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO-NEUTRO] Valores salvos: NBI=None, SIL=None
2025-05-25 12:39:37 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - TERCIARIO] Opções FILTRADAS por Um=24.0 e Norma=IEC.
2025-05-25 12:39:37 - callbacks.global_updates - WARNING - Dados essenciais ausentes no transformer_store_data
2025-05-25 12:39:37 - components.transformer_info_template - WARNING - transformer_data inválido ou vazio: {}
2025-05-25 12:39:37 - callbacks.global_updates - DEBUG - Painel atualizado em 3.0ms
2025-05-25 12:39:37 - callbacks.losses - CRITICAL - [LOSSES POPULATE VAZIO] Acionado. Aba: tab-vazio, Trigger: N/A
2025-05-25 12:39:37 - layouts.impulse - INFO - Criando layout de impulso...
2025-05-25 12:39:37 - layouts.impulse - INFO - [Impulse] Dados do transformador obtidos do MCP: 58
2025-05-25 12:39:37 - callbacks.global_updates - WARNING - Dados essenciais ausentes no transformer_store_data
2025-05-25 12:39:37 - components.transformer_info_template - WARNING - transformer_data inválido ou vazio: {}
2025-05-25 12:39:37 - callbacks.global_updates - DEBUG - Painel atualizado em 0.0ms
2025-05-25 12:39:38 - layouts.dieletric_analysis - INFO - Creating Dielectric Analysis layout (from dieletric_analysis.py)...
2025-05-25 12:39:38 - app_core.standards - ERROR - Arquivo JSON 'c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\tabela.json' não encontrado.
2025-05-25 12:39:38 - app_core.standards - ERROR - Arquivo JSON 'c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\tabela.json' não encontrado.
2025-05-25 12:39:38 - app_core.standards - ERROR - Arquivo JSON 'c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\tabela.json' não encontrado.
2025-05-25 12:39:38 - app_core.standards - ERROR - Arquivo JSON 'c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\tabela.json' não encontrado.
2025-05-25 12:39:38 - app_core.standards - CRITICAL - Falha CRÍTICA ao inicializar as classes das normas: Falha ao carregar dados JSON para NBR ou IEEE.
Traceback (most recent call last):
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\app_core\standards.py", line 613, in __init__
    raise RuntimeError("Falha ao carregar dados JSON para NBR ou IEEE.")
RuntimeError: Falha ao carregar dados JSON para NBR ou IEEE.
2025-05-25 12:39:38 - layouts.dieletric_analysis - WARNING - VerificadorTransformador is invalid upon creation in create_dielectric_layout.
2025-05-25 12:39:38 - components.ui_elements - WARNING - Verificador inválido ao criar coluna dielétrica (Alta Tensão).
2025-05-25 12:39:38 - components.ui_elements - WARNING - Verificador inválido ao criar coluna dielétrica (Baixa Tensão).
2025-05-25 12:39:38 - components.ui_elements - WARNING - Verificador inválido ao criar coluna dielétrica (Terciário).
2025-05-25 12:39:38 - callbacks.global_updates - WARNING - Dados essenciais ausentes no transformer_store_data
2025-05-25 12:39:38 - components.transformer_info_template - WARNING - transformer_data inválido ou vazio: {}
2025-05-25 12:39:38 - callbacks.global_updates - DEBUG - Painel atualizado em 1.0ms
2025-05-25 12:39:38 - app_core.standards - ERROR - Arquivo JSON 'c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\tabela.json' não encontrado.
2025-05-25 12:39:38 - app_core.standards - ERROR - Arquivo JSON 'c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\tabela.json' não encontrado.
2025-05-25 12:39:38 - app_core.standards - ERROR - Arquivo JSON 'c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\tabela.json' não encontrado.
2025-05-25 12:39:38 - app_core.standards - ERROR - Arquivo JSON 'c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\tabela.json' não encontrado.
2025-05-25 12:39:38 - app_core.standards - CRITICAL - Falha CRÍTICA ao inicializar as classes das normas: Falha ao carregar dados JSON para NBR ou IEEE.
Traceback (most recent call last):
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\app_core\standards.py", line 613, in __init__
    raise RuntimeError("Falha ao carregar dados JSON para NBR ou IEEE.")
RuntimeError: Falha ao carregar dados JSON para NBR ou IEEE.
2025-05-25 12:39:38 - callbacks.dieletric_analysis - ERROR - Instância VerificadorTransformador criada, mas inválida (falha dados?).
2025-05-25 12:39:38 - layouts.applied_voltage - INFO - [Applied Voltage Layout] Creating layout...
2025-05-25 12:39:38 - callbacks.global_updates - WARNING - Dados essenciais ausentes no transformer_store_data
2025-05-25 12:39:38 - app - ERROR - Exception on /_dash-update-component [POST]
Traceback (most recent call last):
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\flask\app.py", line 2190, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\flask\app.py", line 1486, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\dash\dash.py", line 1373, in dispatch
    ctx.run(
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\dash\_callback.py", line 465, in add_context
    output_value = _invoke_callback(func, *func_args, **func_kwargs)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\dash\_callback.py", line 40, in _invoke_callback
    return func(*args, **kwargs)  # %% callback invoked %%
           ^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\callbacks\applied_voltage.py", line 821, in load_applied_voltage_inputs
    raise dash.no_update
TypeError: exceptions must derive from BaseException
2025-05-25 12:39:38 - components.transformer_info_template - WARNING - transformer_data inválido ou vazio: {}
2025-05-25 12:39:38 - callbacks.global_updates - DEBUG - Painel atualizado em 3.6ms
2025-05-25 12:39:39 - app - ERROR - Exception on /_dash-update-component [POST]
Traceback (most recent call last):
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\flask\app.py", line 2190, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\flask\app.py", line 1486, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\dash\dash.py", line 1373, in dispatch
    ctx.run(
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\dash\_callback.py", line 465, in add_context
    output_value = _invoke_callback(func, *func_args, **func_kwargs)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\dash\_callback.py", line 40, in _invoke_callback
    return func(*args, **kwargs)  # %% callback invoked %%
           ^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\callbacks\applied_voltage.py", line 784, in load_applied_voltage_inputs
    raise dash.no_update
TypeError: exceptions must derive from BaseException
2025-05-25 12:39:39 - layouts.induced_voltage - INFO - [Induced Voltage] Usando dados vazios inicialmente, serão preenchidos via callback
2025-05-25 12:39:39 - components.ui_elements - WARNING - Style fornecido tanto via input_style quanto via kwargs para frequencia-teste. Usando input_style.
2025-05-25 12:39:39 - components.ui_elements - WARNING - Style fornecido tanto via input_style quanto via kwargs para capacitancia. Usando input_style.
2025-05-25 12:39:39 - callbacks.global_updates - WARNING - Dados essenciais ausentes no transformer_store_data
2025-05-25 12:39:39 - components.transformer_info_template - WARNING - transformer_data inválido ou vazio: {}
2025-05-25 12:39:39 - callbacks.global_updates - DEBUG - Painel atualizado em 0.0ms
2025-05-25 12:39:40 - layouts.short_circuit - INFO - [Short Circuit] Dados do transformador obtidos do MCP: 58
2025-05-25 12:39:40 - callbacks.global_updates - WARNING - Dados essenciais ausentes no transformer_store_data
2025-05-25 12:39:40 - components.transformer_info_template - WARNING - transformer_data inválido ou vazio: {}
2025-05-25 12:39:40 - callbacks.global_updates - DEBUG - Painel atualizado em 4.24ms
2025-05-25 12:39:41 - layouts.applied_voltage - INFO - [Applied Voltage Layout] Creating layout...
2025-05-25 12:39:41 - callbacks.global_updates - WARNING - Dados essenciais ausentes no transformer_store_data
2025-05-25 12:39:41 - components.transformer_info_template - WARNING - transformer_data inválido ou vazio: {}
2025-05-25 12:39:41 - app - ERROR - Exception on /_dash-update-component [POST]
Traceback (most recent call last):
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\flask\app.py", line 2190, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\flask\app.py", line 1486, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\dash\dash.py", line 1373, in dispatch
    ctx.run(
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\dash\_callback.py", line 465, in add_context
    output_value = _invoke_callback(func, *func_args, **func_kwargs)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\dash\_callback.py", line 40, in _invoke_callback
    return func(*args, **kwargs)  # %% callback invoked %%
           ^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\callbacks\applied_voltage.py", line 821, in load_applied_voltage_inputs
    raise dash.no_update
TypeError: exceptions must derive from BaseException
2025-05-25 12:39:41 - callbacks.global_updates - DEBUG - Painel atualizado em 1.0ms
2025-05-25 12:39:41 - app - ERROR - Exception on /_dash-update-component [POST]
Traceback (most recent call last):
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\flask\app.py", line 2190, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\flask\app.py", line 1486, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\dash\dash.py", line 1373, in dispatch
    ctx.run(
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\dash\_callback.py", line 465, in add_context
    output_value = _invoke_callback(func, *func_args, **func_kwargs)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\dash\_callback.py", line 40, in _invoke_callback
    return func(*args, **kwargs)  # %% callback invoked %%
           ^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\callbacks\applied_voltage.py", line 784, in load_applied_voltage_inputs
    raise dash.no_update
TypeError: exceptions must derive from BaseException
2025-05-25 12:39:41 - layouts.dieletric_analysis - INFO - Creating Dielectric Analysis layout (from dieletric_analysis.py)...
2025-05-25 12:39:41 - app_core.standards - ERROR - Arquivo JSON 'c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\tabela.json' não encontrado.
2025-05-25 12:39:41 - app_core.standards - ERROR - Arquivo JSON 'c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\tabela.json' não encontrado.
2025-05-25 12:39:41 - app_core.standards - ERROR - Arquivo JSON 'c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\tabela.json' não encontrado.
2025-05-25 12:39:41 - app_core.standards - ERROR - Arquivo JSON 'c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\tabela.json' não encontrado.
2025-05-25 12:39:41 - app_core.standards - CRITICAL - Falha CRÍTICA ao inicializar as classes das normas: Falha ao carregar dados JSON para NBR ou IEEE.
Traceback (most recent call last):
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\app_core\standards.py", line 613, in __init__
    raise RuntimeError("Falha ao carregar dados JSON para NBR ou IEEE.")
RuntimeError: Falha ao carregar dados JSON para NBR ou IEEE.
2025-05-25 12:39:41 - layouts.dieletric_analysis - WARNING - VerificadorTransformador is invalid upon creation in create_dielectric_layout.
2025-05-25 12:39:41 - components.ui_elements - WARNING - Verificador inválido ao criar coluna dielétrica (Alta Tensão).
2025-05-25 12:39:41 - components.ui_elements - WARNING - Verificador inválido ao criar coluna dielétrica (Baixa Tensão).
2025-05-25 12:39:41 - components.ui_elements - WARNING - Verificador inválido ao criar coluna dielétrica (Terciário).
2025-05-25 12:39:41 - callbacks.global_updates - WARNING - Dados essenciais ausentes no transformer_store_data
2025-05-25 12:39:41 - components.transformer_info_template - WARNING - transformer_data inválido ou vazio: {}
2025-05-25 12:39:41 - app_core.standards - ERROR - Arquivo JSON 'c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\tabela.json' não encontrado.
2025-05-25 12:39:41 - callbacks.global_updates - DEBUG - Painel atualizado em 1.0ms
2025-05-25 12:39:41 - app_core.standards - ERROR - Arquivo JSON 'c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\tabela.json' não encontrado.
2025-05-25 12:39:41 - app_core.standards - ERROR - Arquivo JSON 'c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\tabela.json' não encontrado.
2025-05-25 12:39:41 - app_core.standards - ERROR - Arquivo JSON 'c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\tabela.json' não encontrado.
2025-05-25 12:39:41 - app_core.standards - CRITICAL - Falha CRÍTICA ao inicializar as classes das normas: Falha ao carregar dados JSON para NBR ou IEEE.
Traceback (most recent call last):
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\app_core\standards.py", line 613, in __init__
    raise RuntimeError("Falha ao carregar dados JSON para NBR ou IEEE.")
RuntimeError: Falha ao carregar dados JSON para NBR ou IEEE.
2025-05-25 12:39:41 - callbacks.dieletric_analysis - ERROR - Instância VerificadorTransformador criada, mas inválida (falha dados?).
2025-05-25 12:39:41 - layouts.impulse - INFO - Criando layout de impulso...
2025-05-25 12:39:41 - layouts.impulse - INFO - [Impulse] Dados do transformador obtidos do MCP: 58
2025-05-25 12:39:42 - callbacks.global_updates - WARNING - Dados essenciais ausentes no transformer_store_data
2025-05-25 12:39:42 - components.transformer_info_template - WARNING - transformer_data inválido ou vazio: {}
2025-05-25 12:39:42 - callbacks.global_updates - DEBUG - Painel atualizado em 0.0ms
2025-05-25 12:39:42 - utils.mcp_persistence - INFO - [ensure_mcp_data_propagation] Store de origem transformer-inputs-store É a fonte-de-verdade para campos básicos
2025-05-25 12:39:42 - app_core.transformer_mcp - DEBUG - [MCP SET] Dados definidos para store 'losses-store'.
2025-05-25 12:39:42 - utils.mcp_persistence - INFO - [ensure_mcp_data_propagation] Dados básicos propagados para losses-store
2025-05-25 12:39:42 - layouts.losses - INFO - Creating Losses layout...
2025-05-25 12:39:42 - layouts.losses - INFO - [Losses Layout] Dados do transformador obtidos do losses-store.
2025-05-25 12:39:43 - callbacks.global_updates - WARNING - Dados essenciais ausentes no transformer_store_data
2025-05-25 12:39:43 - components.transformer_info_template - WARNING - transformer_data inválido ou vazio: {}
2025-05-25 12:39:43 - callbacks.global_updates - DEBUG - Painel atualizado em 2.65ms
2025-05-25 12:39:43 - callbacks.losses - CRITICAL - [LOSSES POPULATE VAZIO] Acionado. Aba: tab-vazio, Trigger: N/A
2025-05-25 12:39:43 - layouts.transformer_inputs - INFO - Criando layout Dados Básicos (v5 - Layout Geral e Pesos em 2 linhas)...
2025-05-25 12:39:43 - layouts.transformer_inputs - INFO - Classes de tensão carregadas para dropdown: 27 opções.
2025-05-25 12:39:43 - callbacks.global_updates - WARNING - Dados essenciais ausentes no transformer_store_data
2025-05-25 12:39:43 - components.transformer_info_template - WARNING - transformer_data inválido ou vazio: {}
2025-05-25 12:39:43 - callbacks.transformer_inputs - INFO - [LOAD TransformerInputs] Acionado por: None
2025-05-25 12:39:43 - callbacks.global_updates - DEBUG - Painel atualizado em 1.0ms
2025-05-25 12:39:43 - callbacks.transformer_inputs - INFO - [LOAD TransformerInputs] Opções dos dropdowns ainda não foram populadas, aguardando...
2025-05-25 12:39:43 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_kv: 69, Tensão: 69, Path: /dados
2025-05-25 12:39:43 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT-NEUTRO] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_Neutro: None, Conexão: triangulo, Tensão: 69
2025-05-25 12:39:43 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT] Valores do MCP: NBI=None, SIL=None, TA=None, TI=None
2025-05-25 12:39:43 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT-NEUTRO] Valores do MCP: NBI=None, SIL=None
2025-05-25 12:39:43 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_kv: 3.6, Tensão: 13.8, Path: /dados
2025-05-25 12:39:43 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT] Valores salvos: NBI=None, SIL=None, TA=None, TI=
2025-05-25 12:39:43 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT-NEUTRO] Valores salvos: NBI=None, SIL=None
2025-05-25 12:39:43 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT-NEUTRO] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_Neutro: None, Conexão: triangulo, Tensão: 13.8
2025-05-25 12:39:43 - app_core.isolation_repo - WARNING - [ISOLATION] Nenhum registro encontrado para Um=69.0kV e norma=IEC/NBR. Usando valores padrão.
2025-05-25 12:39:43 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_kv: 24, Tensão: 13, Path: /dados
2025-05-25 12:39:43 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT] Valores do MCP: NBI=None, SIL=None, TA=None, TI=None
2025-05-25 12:39:43 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO-NEUTRO] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_Neutro: None, Conexão: triangulo, Tensão: 13
2025-05-25 12:39:43 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT-NEUTRO] Valores do MCP: NBI=None, SIL=None
2025-05-25 12:39:43 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - AT] Opções FILTRADAS por Um=69.0 e Norma=IEC.
2025-05-25 12:39:43 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO] Valores do MCP: NBI=None, SIL=None, TA=None, TI=None
2025-05-25 12:39:43 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT] Valores salvos: NBI=None, SIL=None, TA=None, TI=/dados
2025-05-25 12:39:43 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO-NEUTRO] Valores do MCP: NBI=None, SIL=None
2025-05-25 12:39:43 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT-NEUTRO] Valores salvos: NBI=None, SIL=None
2025-05-25 12:39:43 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO] Valores salvos: NBI=None, SIL=None, TA=None, TI=/dados
2025-05-25 12:39:43 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO-NEUTRO] Valores salvos: NBI=None, SIL=None
2025-05-25 12:39:43 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - BT] Opções FILTRADAS por Um=3.6 e Norma=IEC.
2025-05-25 12:39:43 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - TERCIARIO] Opções FILTRADAS por Um=24.0 e Norma=IEC.
2025-05-25 12:39:44 - callbacks.transformer_inputs - INFO - [LOAD TransformerInputs] Acionado por: url
2025-05-25 12:39:44 - callbacks.transformer_inputs - DEBUG - [LOAD TransformerInputs] Não estamos na página de dados (pathname=/impulso, clean_path=impulso), prevenindo atualização
2025-05-25 12:39:44 - layouts.impulse - INFO - Criando layout de impulso...
2025-05-25 12:39:44 - layouts.impulse - INFO - [Impulse] Dados do transformador obtidos do MCP: 58
2025-05-25 12:39:44 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT] Acionado por: url. Norma: IEC, Um_kv: 69, Tensão: 69, Path: /impulso
2025-05-25 12:39:44 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT] Valores do MCP: NBI=None, SIL=None, TA=None, TI=None
2025-05-25 12:39:44 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT] Acionado por: url. Norma: IEC, Um_kv: 3.6, Tensão: 13.8, Path: /impulso
2025-05-25 12:39:44 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO] Acionado por: url. Norma: IEC, Um_kv: 24, Tensão: 13, Path: /impulso
2025-05-25 12:39:44 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT] Valores salvos: NBI=None, SIL=None, TA=None, TI=
2025-05-25 12:39:44 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT] Valores do MCP: NBI=None, SIL=None, TA=None, TI=None
2025-05-25 12:39:44 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT-NEUTRO] Acionado por: url. Norma: IEC, Um_Neutro: None, Conexão: triangulo, Tensão: 69
2025-05-25 12:39:44 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO] Valores do MCP: NBI=None, SIL=None, TA=None, TI=None
2025-05-25 12:39:44 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT] Valores salvos: NBI=None, SIL=None, TA=None, TI=/impulso
2025-05-25 12:39:44 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT-NEUTRO] Acionado por: url. Norma: IEC, Um_Neutro: None, Conexão: triangulo, Tensão: 13.8
2025-05-25 12:39:44 - app_core.isolation_repo - WARNING - [ISOLATION] Nenhum registro encontrado para Um=69.0kV e norma=IEC/NBR. Usando valores padrão.
2025-05-25 12:39:44 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT-NEUTRO] Valores do MCP: NBI=None, SIL=None
2025-05-25 12:39:44 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO-NEUTRO] Acionado por: url. Norma: IEC, Um_Neutro: None, Conexão: triangulo, Tensão: 13
2025-05-25 12:39:44 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO] Valores salvos: NBI=None, SIL=None, TA=None, TI=/impulso
2025-05-25 12:39:44 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT-NEUTRO] Valores do MCP: NBI=None, SIL=None
2025-05-25 12:39:44 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - BT] Opções FILTRADAS por Um=3.6 e Norma=IEC.
2025-05-25 12:39:44 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - AT] Opções FILTRADAS por Um=69.0 e Norma=IEC.
2025-05-25 12:39:44 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT-NEUTRO] Valores salvos: NBI=None, SIL=None
2025-05-25 12:39:44 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO-NEUTRO] Valores do MCP: NBI=None, SIL=None
2025-05-25 12:39:44 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT-NEUTRO] Valores salvos: NBI=None, SIL=None
2025-05-25 12:39:44 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - TERCIARIO] Opções FILTRADAS por Um=24.0 e Norma=IEC.
2025-05-25 12:39:44 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO-NEUTRO] Valores salvos: NBI=None, SIL=None
2025-05-25 12:39:44 - callbacks.global_updates - WARNING - Dados essenciais ausentes no transformer_store_data
2025-05-25 12:39:44 - components.transformer_info_template - WARNING - transformer_data inválido ou vazio: {}
2025-05-25 12:39:44 - callbacks.global_updates - DEBUG - Painel atualizado em 1.0ms
2025-05-25 12:39:45 - layouts.dieletric_analysis - INFO - Creating Dielectric Analysis layout (from dieletric_analysis.py)...
2025-05-25 12:39:45 - app_core.standards - ERROR - Arquivo JSON 'c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\tabela.json' não encontrado.
2025-05-25 12:39:45 - app_core.standards - ERROR - Arquivo JSON 'c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\tabela.json' não encontrado.
2025-05-25 12:39:45 - app_core.standards - ERROR - Arquivo JSON 'c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\tabela.json' não encontrado.
2025-05-25 12:39:45 - app_core.standards - ERROR - Arquivo JSON 'c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\tabela.json' não encontrado.
2025-05-25 12:39:45 - app_core.standards - CRITICAL - Falha CRÍTICA ao inicializar as classes das normas: Falha ao carregar dados JSON para NBR ou IEEE.
Traceback (most recent call last):
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\app_core\standards.py", line 613, in __init__
    raise RuntimeError("Falha ao carregar dados JSON para NBR ou IEEE.")
RuntimeError: Falha ao carregar dados JSON para NBR ou IEEE.
2025-05-25 12:39:45 - layouts.dieletric_analysis - WARNING - VerificadorTransformador is invalid upon creation in create_dielectric_layout.
2025-05-25 12:39:45 - components.ui_elements - WARNING - Verificador inválido ao criar coluna dielétrica (Alta Tensão).
2025-05-25 12:39:45 - components.ui_elements - WARNING - Verificador inválido ao criar coluna dielétrica (Baixa Tensão).
2025-05-25 12:39:45 - components.ui_elements - WARNING - Verificador inválido ao criar coluna dielétrica (Terciário).
2025-05-25 12:39:45 - callbacks.global_updates - WARNING - Dados essenciais ausentes no transformer_store_data
2025-05-25 12:39:45 - components.transformer_info_template - WARNING - transformer_data inválido ou vazio: {}
2025-05-25 12:39:45 - app_core.standards - ERROR - Arquivo JSON 'c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\tabela.json' não encontrado.
2025-05-25 12:39:45 - callbacks.global_updates - DEBUG - Painel atualizado em 1.0ms
2025-05-25 12:39:45 - app_core.standards - ERROR - Arquivo JSON 'c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\tabela.json' não encontrado.
2025-05-25 12:39:45 - app_core.standards - ERROR - Arquivo JSON 'c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\tabela.json' não encontrado.
2025-05-25 12:39:45 - app_core.standards - ERROR - Arquivo JSON 'c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\tabela.json' não encontrado.
2025-05-25 12:39:45 - app_core.standards - CRITICAL - Falha CRÍTICA ao inicializar as classes das normas: Falha ao carregar dados JSON para NBR ou IEEE.
Traceback (most recent call last):
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\app_core\standards.py", line 613, in __init__
    raise RuntimeError("Falha ao carregar dados JSON para NBR ou IEEE.")
RuntimeError: Falha ao carregar dados JSON para NBR ou IEEE.
2025-05-25 12:39:45 - callbacks.dieletric_analysis - ERROR - Instância VerificadorTransformador criada, mas inválida (falha dados?).
2025-05-25 12:39:50 - layouts.applied_voltage - INFO - [Applied Voltage Layout] Creating layout...
2025-05-25 12:39:50 - callbacks.global_updates - WARNING - Dados essenciais ausentes no transformer_store_data
2025-05-25 12:39:50 - components.transformer_info_template - WARNING - transformer_data inválido ou vazio: {}
2025-05-25 12:39:50 - app - ERROR - Exception on /_dash-update-component [POST]
Traceback (most recent call last):
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\flask\app.py", line 2190, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\flask\app.py", line 1486, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\dash\dash.py", line 1373, in dispatch
    ctx.run(
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\dash\_callback.py", line 465, in add_context
    output_value = _invoke_callback(func, *func_args, **func_kwargs)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\dash\_callback.py", line 40, in _invoke_callback
    return func(*args, **kwargs)  # %% callback invoked %%
           ^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\callbacks\applied_voltage.py", line 821, in load_applied_voltage_inputs
    raise dash.no_update
TypeError: exceptions must derive from BaseException
2025-05-25 12:39:50 - callbacks.global_updates - DEBUG - Painel atualizado em 1.0ms
2025-05-25 12:39:51 - app - ERROR - Exception on /_dash-update-component [POST]
Traceback (most recent call last):
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\flask\app.py", line 2190, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\flask\app.py", line 1486, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\dash\dash.py", line 1373, in dispatch
    ctx.run(
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\dash\_callback.py", line 465, in add_context
    output_value = _invoke_callback(func, *func_args, **func_kwargs)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\dash\_callback.py", line 40, in _invoke_callback
    return func(*args, **kwargs)  # %% callback invoked %%
           ^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\callbacks\applied_voltage.py", line 784, in load_applied_voltage_inputs
    raise dash.no_update
TypeError: exceptions must derive from BaseException
2025-05-25 12:39:51 - layouts.induced_voltage - INFO - [Induced Voltage] Usando dados vazios inicialmente, serão preenchidos via callback
2025-05-25 12:39:51 - components.ui_elements - WARNING - Style fornecido tanto via input_style quanto via kwargs para frequencia-teste. Usando input_style.
2025-05-25 12:39:51 - components.ui_elements - WARNING - Style fornecido tanto via input_style quanto via kwargs para capacitancia. Usando input_style.
2025-05-25 12:39:51 - callbacks.global_updates - WARNING - Dados essenciais ausentes no transformer_store_data
2025-05-25 12:39:51 - components.transformer_info_template - WARNING - transformer_data inválido ou vazio: {}
2025-05-25 12:39:51 - callbacks.global_updates - DEBUG - Painel atualizado em 1.0ms
2025-05-25 12:39:51 - layouts.short_circuit - INFO - [Short Circuit] Dados do transformador obtidos do MCP: 58
2025-05-25 12:39:51 - callbacks.global_updates - WARNING - Dados essenciais ausentes no transformer_store_data
2025-05-25 12:39:51 - components.transformer_info_template - WARNING - transformer_data inválido ou vazio: {}
2025-05-25 12:39:51 - callbacks.global_updates - DEBUG - Painel atualizado em 10.1ms
2025-05-25 12:39:52 - layouts.temperature_rise - INFO - [Temperature Rise] Dados do transformador obtidos do MCP: 58
2025-05-25 12:39:52 - callbacks.global_updates - WARNING - Dados essenciais ausentes no transformer_store_data
2025-05-25 12:39:52 - components.transformer_info_template - WARNING - transformer_data inválido ou vazio: {}
2025-05-25 12:39:52 - callbacks.global_updates - DEBUG - Painel atualizado em 0.0ms
2025-05-25 12:39:57 - layouts.transformer_inputs - INFO - Criando layout Dados Básicos (v5 - Layout Geral e Pesos em 2 linhas)...
2025-05-25 12:39:57 - layouts.transformer_inputs - INFO - Classes de tensão carregadas para dropdown: 27 opções.
2025-05-25 12:39:57 - callbacks.global_updates - WARNING - Dados essenciais ausentes no transformer_store_data
2025-05-25 12:39:57 - components.transformer_info_template - WARNING - transformer_data inválido ou vazio: {}
2025-05-25 12:39:57 - callbacks.global_updates - DEBUG - Painel atualizado em 1.0ms
2025-05-25 12:39:57 - callbacks.transformer_inputs - INFO - [LOAD TransformerInputs] Acionado por: None
2025-05-25 12:39:57 - callbacks.transformer_inputs - INFO - [LOAD TransformerInputs] Opções dos dropdowns ainda não foram populadas, aguardando...
2025-05-25 12:39:57 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_kv: 69, Tensão: 69, Path: /dados
2025-05-25 12:39:57 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT] Valores do MCP: NBI=None, SIL=None, TA=None, TI=None
2025-05-25 12:39:57 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT-NEUTRO] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_Neutro: None, Conexão: triangulo, Tensão: 69
2025-05-25 12:39:57 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT] Valores salvos: NBI=None, SIL=None, TA=None, TI=
2025-05-25 12:39:57 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT-NEUTRO] Valores do MCP: NBI=None, SIL=None
2025-05-25 12:39:57 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_kv: 3.6, Tensão: 13.8, Path: /dados
2025-05-25 12:39:57 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT-NEUTRO] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_Neutro: None, Conexão: triangulo, Tensão: 13.8
2025-05-25 12:39:57 - app_core.isolation_repo - WARNING - [ISOLATION] Nenhum registro encontrado para Um=69.0kV e norma=IEC/NBR. Usando valores padrão.
2025-05-25 12:39:57 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT-NEUTRO] Valores salvos: NBI=None, SIL=None
2025-05-25 12:39:57 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT] Valores do MCP: NBI=None, SIL=None, TA=None, TI=None
2025-05-25 12:39:57 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_kv: 24, Tensão: 13, Path: /dados
2025-05-25 12:39:57 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT-NEUTRO] Valores do MCP: NBI=None, SIL=None
2025-05-25 12:39:57 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - AT] Opções FILTRADAS por Um=69.0 e Norma=IEC.
2025-05-25 12:39:57 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO-NEUTRO] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_Neutro: None, Conexão: triangulo, Tensão: 13
2025-05-25 12:39:57 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT] Valores salvos: NBI=None, SIL=None, TA=None, TI=/dados
2025-05-25 12:39:57 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO] Valores do MCP: NBI=None, SIL=None, TA=None, TI=None
2025-05-25 12:39:57 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT-NEUTRO] Valores salvos: NBI=None, SIL=None
2025-05-25 12:39:57 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO-NEUTRO] Valores do MCP: NBI=None, SIL=None
2025-05-25 12:39:57 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO] Valores salvos: NBI=None, SIL=None, TA=None, TI=/dados
2025-05-25 12:39:57 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - BT] Opções FILTRADAS por Um=3.6 e Norma=IEC.
2025-05-25 12:39:57 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO-NEUTRO] Valores salvos: NBI=None, SIL=None
2025-05-25 12:39:57 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - TERCIARIO] Opções FILTRADAS por Um=24.0 e Norma=IEC.
2025-05-25 12:41:33 - layouts.transformer_inputs - INFO - Criando layout Dados Básicos (v5 - Layout Geral e Pesos em 2 linhas)...
2025-05-25 12:41:33 - layouts.transformer_inputs - INFO - Classes de tensão carregadas para dropdown: 27 opções.
2025-05-25 12:41:33 - callbacks.global_updates - DEBUG - Painel atualizado em 1.3ms
2025-05-25 12:41:33 - callbacks.global_updates - DEBUG - Painel atualizado em 0.0ms
2025-05-25 12:41:33 - callbacks.transformer_inputs - INFO - [LOAD TransformerInputs] Acionado por: None
2025-05-25 12:41:33 - callbacks.transformer_inputs - INFO - [LOAD TransformerInputs] Opções dos dropdowns ainda não foram populadas, aguardando...
2025-05-25 12:41:33 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_kv: None, Tensão: None, Path: /
2025-05-25 12:41:33 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT-NEUTRO] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_Neutro: None, Conexão: estrela, Tensão: None
2025-05-25 12:41:33 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT] Valores do MCP: NBI=None, SIL=None, TA=None, TI=None
2025-05-25 12:41:33 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_kv: None, Tensão: None, Path: /
2025-05-25 12:41:33 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT-NEUTRO] Valores do MCP: NBI=None, SIL=None
2025-05-25 12:41:33 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT-NEUTRO] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_Neutro: None, Conexão: triangulo, Tensão: None
2025-05-25 12:41:33 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT] Valores salvos: NBI=None, SIL=None, TA=None, TI=None
2025-05-25 12:41:33 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_kv: None, Tensão: None, Path: /
2025-05-25 12:41:33 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT] Valores do MCP: NBI=None, SIL=None, TA=None, TI=None
2025-05-25 12:41:33 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT-NEUTRO] Valores salvos: NBI=None, SIL=None
2025-05-25 12:41:33 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT-NEUTRO] Valores do MCP: NBI=None, SIL=None
2025-05-25 12:41:33 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO-NEUTRO] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_Neutro: None, Conexão:  , Tensão: None
2025-05-25 12:41:33 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - AT] Um NÃO definido. Populando com opções GERAIS para Norma=IEC.
2025-05-25 12:41:33 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO] Valores do MCP: NBI=None, SIL=None, TA=None, TI=None
2025-05-25 12:41:33 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT] Valores salvos: NBI=None, SIL=None, TA=None, TI=/
2025-05-25 12:41:33 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - AT-NEUTRO] Um_Neutro NÃO definido. Populando com opções GERAIS para Norma=IEC.
2025-05-25 12:41:33 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT-NEUTRO] Valores salvos: NBI=None, SIL=None
2025-05-25 12:41:33 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO-NEUTRO] Valores do MCP: NBI=None, SIL=None
2025-05-25 12:41:33 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO] Valores salvos: NBI=None, SIL=None, TA=None, TI=/
2025-05-25 12:41:33 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - BT] Um NÃO definido. Populando com opções GERAIS para Norma=IEC.
2025-05-25 12:41:33 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO-NEUTRO] Valores salvos: NBI=None, SIL=None
2025-05-25 12:41:33 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - TERCIARIO] Um NÃO definido. Populando com opções GERAIS para Norma=IEC.
2025-05-25 12:41:38 - callbacks.transformer_inputs - INFO - [LOAD TransformerInputs] Acionado por: url
2025-05-25 12:41:38 - callbacks.transformer_inputs - DEBUG - [LOAD TransformerInputs] Não estamos na página de dados (pathname=/perdas, clean_path=perdas), prevenindo atualização
2025-05-25 12:41:38 - utils.mcp_persistence - INFO - [ensure_mcp_data_propagation] Store de origem transformer-inputs-store É a fonte-de-verdade para campos básicos
2025-05-25 12:41:38 - app_core.transformer_mcp - DEBUG - [MCP SET] Dados definidos para store 'losses-store'.
2025-05-25 12:41:38 - utils.mcp_persistence - INFO - [ensure_mcp_data_propagation] Dados básicos propagados para losses-store
2025-05-25 12:41:38 - layouts.losses - INFO - Creating Losses layout...
2025-05-25 12:41:38 - layouts.losses - INFO - [Losses Layout] Dados do transformador obtidos do losses-store.
2025-05-25 12:41:38 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT] Acionado por: url. Norma: IEC, Um_kv: None, Tensão: None, Path: /perdas
2025-05-25 12:41:38 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT] Valores do MCP: NBI=None, SIL=None, TA=None, TI=None
2025-05-25 12:41:38 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT] Valores salvos: NBI=None, SIL=None, TA=None, TI=None
2025-05-25 12:41:38 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - AT] Um NÃO definido. Populando com opções GERAIS para Norma=IEC.
2025-05-25 12:41:38 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT] Acionado por: url. Norma: IEC, Um_kv: None, Tensão: None, Path: /perdas
2025-05-25 12:41:38 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO] Acionado por: url. Norma: IEC, Um_kv: None, Tensão: None, Path: /perdas
2025-05-25 12:41:38 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT] Valores do MCP: NBI=None, SIL=None, TA=None, TI=None
2025-05-25 12:41:38 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO] Valores do MCP: NBI=None, SIL=None, TA=None, TI=None
2025-05-25 12:41:38 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT-NEUTRO] Acionado por: url. Norma: IEC, Um_Neutro: None, Conexão: estrela, Tensão: None
2025-05-25 12:41:38 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT] Valores salvos: NBI=None, SIL=None, TA=None, TI=/perdas
2025-05-25 12:41:38 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO] Valores salvos: NBI=None, SIL=None, TA=None, TI=/perdas
2025-05-25 12:41:38 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT-NEUTRO] Valores do MCP: NBI=None, SIL=None
2025-05-25 12:41:38 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - BT] Um NÃO definido. Populando com opções GERAIS para Norma=IEC.
2025-05-25 12:41:38 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - TERCIARIO] Um NÃO definido. Populando com opções GERAIS para Norma=IEC.
2025-05-25 12:41:38 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT-NEUTRO] Valores salvos: NBI=None, SIL=None
2025-05-25 12:41:38 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - AT-NEUTRO] Um_Neutro NÃO definido. Populando com opções GERAIS para Norma=IEC.
2025-05-25 12:41:38 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT-NEUTRO] Acionado por: url. Norma: IEC, Um_Neutro: None, Conexão: triangulo, Tensão: None
2025-05-25 12:41:38 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO-NEUTRO] Acionado por: url. Norma: IEC, Um_Neutro: None, Conexão:  , Tensão: None
2025-05-25 12:41:38 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT-NEUTRO] Valores do MCP: NBI=None, SIL=None
2025-05-25 12:41:38 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO-NEUTRO] Valores do MCP: NBI=None, SIL=None
2025-05-25 12:41:38 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT-NEUTRO] Valores salvos: NBI=None, SIL=None
2025-05-25 12:41:38 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO-NEUTRO] Valores salvos: NBI=None, SIL=None
2025-05-25 12:41:38 - callbacks.global_updates - DEBUG - Painel atualizado em 1.04ms
2025-05-25 12:41:39 - callbacks.losses - CRITICAL - [LOSSES POPULATE VAZIO] Acionado. Aba: tab-vazio, Trigger: N/A
2025-05-25 12:42:31 - layouts.impulse - INFO - Criando layout de impulso...
2025-05-25 12:42:31 - layouts.impulse - INFO - [Impulse] Dados do transformador obtidos do MCP: 58
2025-05-25 12:42:31 - callbacks.global_updates - DEBUG - Painel atualizado em 1.0ms
2025-05-25 12:42:41 - layouts.dieletric_analysis - INFO - Creating Dielectric Analysis layout (from dieletric_analysis.py)...
2025-05-25 12:42:41 - app_core.standards - ERROR - Arquivo JSON 'c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\tabela.json' não encontrado.
2025-05-25 12:42:41 - app_core.standards - ERROR - Arquivo JSON 'c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\tabela.json' não encontrado.
2025-05-25 12:42:41 - app_core.standards - ERROR - Arquivo JSON 'c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\tabela.json' não encontrado.
2025-05-25 12:42:41 - app_core.standards - ERROR - Arquivo JSON 'c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\tabela.json' não encontrado.
2025-05-25 12:42:41 - app_core.standards - CRITICAL - Falha CRÍTICA ao inicializar as classes das normas: Falha ao carregar dados JSON para NBR ou IEEE.
Traceback (most recent call last):
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\app_core\standards.py", line 613, in __init__
    raise RuntimeError("Falha ao carregar dados JSON para NBR ou IEEE.")
RuntimeError: Falha ao carregar dados JSON para NBR ou IEEE.
2025-05-25 12:42:41 - layouts.dieletric_analysis - WARNING - VerificadorTransformador is invalid upon creation in create_dielectric_layout.
2025-05-25 12:42:41 - components.ui_elements - WARNING - Verificador inválido ao criar coluna dielétrica (Alta Tensão).
2025-05-25 12:42:41 - components.ui_elements - WARNING - Verificador inválido ao criar coluna dielétrica (Baixa Tensão).
2025-05-25 12:42:41 - components.ui_elements - WARNING - Verificador inválido ao criar coluna dielétrica (Terciário).
2025-05-25 12:42:42 - callbacks.global_updates - DEBUG - Painel atualizado em 1.0ms
2025-05-25 12:42:42 - app_core.standards - ERROR - Arquivo JSON 'c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\tabela.json' não encontrado.
2025-05-25 12:42:42 - app_core.standards - ERROR - Arquivo JSON 'c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\tabela.json' não encontrado.
2025-05-25 12:42:42 - app_core.standards - ERROR - Arquivo JSON 'c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\tabela.json' não encontrado.
2025-05-25 12:42:42 - app_core.standards - ERROR - Arquivo JSON 'c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\tabela.json' não encontrado.
2025-05-25 12:42:42 - app_core.standards - CRITICAL - Falha CRÍTICA ao inicializar as classes das normas: Falha ao carregar dados JSON para NBR ou IEEE.
Traceback (most recent call last):
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\app_core\standards.py", line 613, in __init__
    raise RuntimeError("Falha ao carregar dados JSON para NBR ou IEEE.")
RuntimeError: Falha ao carregar dados JSON para NBR ou IEEE.
2025-05-25 12:42:42 - callbacks.dieletric_analysis - ERROR - Instância VerificadorTransformador criada, mas inválida (falha dados?).
2025-05-25 12:42:43 - layouts.applied_voltage - INFO - [Applied Voltage Layout] Creating layout...
2025-05-25 12:42:44 - callbacks.global_updates - DEBUG - Painel atualizado em 0.0ms
2025-05-25 12:42:44 - app - ERROR - Exception on /_dash-update-component [POST]
Traceback (most recent call last):
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\flask\app.py", line 2190, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\flask\app.py", line 1486, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\dash\dash.py", line 1373, in dispatch
    ctx.run(
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\dash\_callback.py", line 465, in add_context
    output_value = _invoke_callback(func, *func_args, **func_kwargs)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\dash\_callback.py", line 40, in _invoke_callback
    return func(*args, **kwargs)  # %% callback invoked %%
           ^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\callbacks\applied_voltage.py", line 821, in load_applied_voltage_inputs
    raise dash.no_update
TypeError: exceptions must derive from BaseException
2025-05-25 12:42:47 - app - ERROR - Exception on /_dash-update-component [POST]
Traceback (most recent call last):
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\flask\app.py", line 2190, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\flask\app.py", line 1486, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\dash\dash.py", line 1373, in dispatch
    ctx.run(
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\dash\_callback.py", line 465, in add_context
    output_value = _invoke_callback(func, *func_args, **func_kwargs)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\dash\_callback.py", line 40, in _invoke_callback
    return func(*args, **kwargs)  # %% callback invoked %%
           ^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\callbacks\applied_voltage.py", line 784, in load_applied_voltage_inputs
    raise dash.no_update
TypeError: exceptions must derive from BaseException
2025-05-25 12:42:47 - layouts.transformer_inputs - INFO - Criando layout Dados Básicos (v5 - Layout Geral e Pesos em 2 linhas)...
2025-05-25 12:42:47 - layouts.transformer_inputs - INFO - Classes de tensão carregadas para dropdown: 27 opções.
2025-05-25 12:42:47 - callbacks.global_updates - DEBUG - Painel atualizado em 0.0ms
2025-05-25 12:42:47 - callbacks.transformer_inputs - INFO - [LOAD TransformerInputs] Acionado por: None
2025-05-25 12:42:47 - callbacks.transformer_inputs - INFO - [LOAD TransformerInputs] Opções dos dropdowns ainda não foram populadas, aguardando...
2025-05-25 12:42:47 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_kv: None, Tensão: None, Path: /dados
2025-05-25 12:42:47 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT] Valores do MCP: NBI=None, SIL=None, TA=None, TI=None
2025-05-25 12:42:47 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT] Valores salvos: NBI=None, SIL=None, TA=None, TI=None
2025-05-25 12:42:47 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - AT] Um NÃO definido. Populando com opções GERAIS para Norma=IEC.
2025-05-25 12:42:47 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT-NEUTRO] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_Neutro: None, Conexão: estrela, Tensão: None
2025-05-25 12:42:47 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_kv: None, Tensão: None, Path: /dados
2025-05-25 12:42:47 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT-NEUTRO] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_Neutro: None, Conexão: triangulo, Tensão: None
2025-05-25 12:42:47 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT-NEUTRO] Valores do MCP: NBI=None, SIL=None
2025-05-25 12:42:47 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT] Valores do MCP: NBI=None, SIL=None, TA=None, TI=None
2025-05-25 12:42:47 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_kv: None, Tensão: None, Path: /dados
2025-05-25 12:42:47 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT-NEUTRO] Valores do MCP: NBI=None, SIL=None
2025-05-25 12:42:47 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO-NEUTRO] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_Neutro: None, Conexão:  , Tensão: None
2025-05-25 12:42:47 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT-NEUTRO] Valores salvos: NBI=None, SIL=None
2025-05-25 12:42:47 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT] Valores salvos: NBI=None, SIL=None, TA=None, TI=/dados
2025-05-25 12:42:47 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO] Valores do MCP: NBI=None, SIL=None, TA=None, TI=None
2025-05-25 12:42:47 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT-NEUTRO] Valores salvos: NBI=None, SIL=None
2025-05-25 12:42:47 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO-NEUTRO] Valores do MCP: NBI=None, SIL=None
2025-05-25 12:42:47 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - AT-NEUTRO] Um_Neutro NÃO definido. Populando com opções GERAIS para Norma=IEC.
2025-05-25 12:42:47 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - BT] Um NÃO definido. Populando com opções GERAIS para Norma=IEC.
2025-05-25 12:42:47 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO] Valores salvos: NBI=None, SIL=None, TA=None, TI=/dados
2025-05-25 12:42:47 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO-NEUTRO] Valores salvos: NBI=None, SIL=None
2025-05-25 12:42:47 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - TERCIARIO] Um NÃO definido. Populando com opções GERAIS para Norma=IEC.
2025-05-25 12:42:48 - callbacks.transformer_inputs - INFO - [LOAD TransformerInputs] Acionado por: url
2025-05-25 12:42:48 - callbacks.transformer_inputs - DEBUG - [LOAD TransformerInputs] Não estamos na página de dados (pathname=/perdas, clean_path=perdas), prevenindo atualização
2025-05-25 12:42:48 - utils.mcp_persistence - INFO - [ensure_mcp_data_propagation] Store de origem transformer-inputs-store É a fonte-de-verdade para campos básicos
2025-05-25 12:42:48 - app_core.transformer_mcp - DEBUG - [MCP SET] Dados definidos para store 'losses-store'.
2025-05-25 12:42:48 - utils.mcp_persistence - INFO - [ensure_mcp_data_propagation] Dados básicos propagados para losses-store
2025-05-25 12:42:48 - layouts.losses - INFO - Creating Losses layout...
2025-05-25 12:42:48 - layouts.losses - INFO - [Losses Layout] Dados do transformador obtidos do losses-store.
2025-05-25 12:42:48 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT] Acionado por: url. Norma: IEC, Um_kv: None, Tensão: None, Path: /perdas
2025-05-25 12:42:48 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT] Valores do MCP: NBI=None, SIL=None, TA=None, TI=None
2025-05-25 12:42:48 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT] Valores salvos: NBI=None, SIL=None, TA=None, TI=None
2025-05-25 12:42:48 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - AT] Um NÃO definido. Populando com opções GERAIS para Norma=IEC.
2025-05-25 12:42:48 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT] Acionado por: url. Norma: IEC, Um_kv: None, Tensão: None, Path: /perdas
2025-05-25 12:42:48 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO] Acionado por: url. Norma: IEC, Um_kv: None, Tensão: None, Path: /perdas
2025-05-25 12:42:48 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT-NEUTRO] Acionado por: url. Norma: IEC, Um_Neutro: None, Conexão: estrela, Tensão: None
2025-05-25 12:42:48 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT] Valores do MCP: NBI=None, SIL=None, TA=None, TI=None
2025-05-25 12:42:48 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO] Valores do MCP: NBI=None, SIL=None, TA=None, TI=None
2025-05-25 12:42:48 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT-NEUTRO] Acionado por: url. Norma: IEC, Um_Neutro: None, Conexão: triangulo, Tensão: None
2025-05-25 12:42:48 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT-NEUTRO] Valores do MCP: NBI=None, SIL=None
2025-05-25 12:42:48 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO-NEUTRO] Acionado por: url. Norma: IEC, Um_Neutro: None, Conexão:  , Tensão: None
2025-05-25 12:42:48 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT] Valores salvos: NBI=None, SIL=None, TA=None, TI=/perdas
2025-05-25 12:42:48 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO] Valores salvos: NBI=None, SIL=None, TA=None, TI=/perdas
2025-05-25 12:42:48 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT-NEUTRO] Valores do MCP: NBI=None, SIL=None
2025-05-25 12:42:48 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT-NEUTRO] Valores salvos: NBI=None, SIL=None
2025-05-25 12:42:48 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO-NEUTRO] Valores do MCP: NBI=None, SIL=None
2025-05-25 12:42:48 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - BT] Um NÃO definido. Populando com opções GERAIS para Norma=IEC.
2025-05-25 12:42:48 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - TERCIARIO] Um NÃO definido. Populando com opções GERAIS para Norma=IEC.
2025-05-25 12:42:48 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT-NEUTRO] Valores salvos: NBI=None, SIL=None
2025-05-25 12:42:48 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - AT-NEUTRO] Um_Neutro NÃO definido. Populando com opções GERAIS para Norma=IEC.
2025-05-25 12:42:48 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO-NEUTRO] Valores salvos: NBI=None, SIL=None
2025-05-25 12:42:49 - callbacks.global_updates - DEBUG - Painel atualizado em 0.0ms
2025-05-25 12:42:49 - callbacks.losses - CRITICAL - [LOSSES POPULATE VAZIO] Acionado. Aba: tab-vazio, Trigger: N/A
2025-05-25 12:43:27 - utils.mcp_persistence - INFO - [ensure_mcp_data_propagation] Store de origem transformer-inputs-store É a fonte-de-verdade para campos básicos
2025-05-25 12:43:27 - app_core.transformer_mcp - DEBUG - [MCP SET] Dados definidos para store 'losses-store'.
2025-05-25 12:43:27 - utils.mcp_persistence - INFO - [ensure_mcp_data_propagation] Dados básicos propagados para losses-store
2025-05-25 12:43:27 - layouts.losses - INFO - Creating Losses layout...
2025-05-25 12:43:27 - layouts.losses - INFO - [Losses Layout] Dados do transformador obtidos do losses-store.
2025-05-25 12:43:27 - callbacks.global_updates - DEBUG - Painel atualizado em 1.49ms
2025-05-25 12:43:28 - callbacks.global_updates - DEBUG - Painel atualizado em 0.0ms
2025-05-25 12:43:28 - callbacks.losses - CRITICAL - [LOSSES POPULATE VAZIO] Acionado. Aba: tab-vazio, Trigger: N/A
2025-05-25 12:43:32 - layouts.transformer_inputs - INFO - Criando layout Dados Básicos (v5 - Layout Geral e Pesos em 2 linhas)...
2025-05-25 12:43:32 - layouts.transformer_inputs - INFO - Classes de tensão carregadas para dropdown: 27 opções.
2025-05-25 12:43:32 - callbacks.global_updates - DEBUG - Painel atualizado em 1.0ms
2025-05-25 12:43:32 - callbacks.transformer_inputs - INFO - [LOAD TransformerInputs] Acionado por: None
2025-05-25 12:43:32 - callbacks.transformer_inputs - INFO - [LOAD TransformerInputs] Opções dos dropdowns ainda não foram populadas, aguardando...
2025-05-25 12:43:32 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_kv: None, Tensão: None, Path: /dados
2025-05-25 12:43:32 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_kv: None, Tensão: None, Path: /dados
2025-05-25 12:43:32 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT-NEUTRO] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_Neutro: None, Conexão: estrela, Tensão: None
2025-05-25 12:43:32 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT-NEUTRO] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_Neutro: None, Conexão: triangulo, Tensão: None
2025-05-25 12:43:32 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT] Valores do MCP: NBI=None, SIL=None, TA=None, TI=None
2025-05-25 12:43:32 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT] Valores do MCP: NBI=None, SIL=None, TA=None, TI=None
2025-05-25 12:43:32 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_kv: None, Tensão: None, Path: /dados
2025-05-25 12:43:32 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT-NEUTRO] Valores do MCP: NBI=None, SIL=None
2025-05-25 12:43:32 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO-NEUTRO] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_Neutro: None, Conexão:  , Tensão: None
2025-05-25 12:43:32 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT-NEUTRO] Valores do MCP: NBI=None, SIL=None
2025-05-25 12:43:32 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT] Valores salvos: NBI=None, SIL=None, TA=None, TI=None
2025-05-25 12:43:32 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT] Valores salvos: NBI=None, SIL=None, TA=None, TI=/dados
2025-05-25 12:43:32 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO] Valores do MCP: NBI=None, SIL=None, TA=None, TI=None
2025-05-25 12:43:32 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT-NEUTRO] Valores salvos: NBI=None, SIL=None
2025-05-25 12:43:32 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO-NEUTRO] Valores do MCP: NBI=None, SIL=None
2025-05-25 12:43:32 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT-NEUTRO] Valores salvos: NBI=None, SIL=None
2025-05-25 12:43:32 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - AT] Um NÃO definido. Populando com opções GERAIS para Norma=IEC.
2025-05-25 12:43:32 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - BT] Um NÃO definido. Populando com opções GERAIS para Norma=IEC.
2025-05-25 12:43:32 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO] Valores salvos: NBI=None, SIL=None, TA=None, TI=/dados
2025-05-25 12:43:32 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - AT-NEUTRO] Um_Neutro NÃO definido. Populando com opções GERAIS para Norma=IEC.
2025-05-25 12:43:32 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO-NEUTRO] Valores salvos: NBI=None, SIL=None
2025-05-25 12:43:32 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - TERCIARIO] Um NÃO definido. Populando com opções GERAIS para Norma=IEC.
2025-05-25 12:46:03 - callbacks.temperature_rise - INFO - Registrando callbacks do módulo temperature_rise para app Simulador de Testes de Transformadores (IEC/IEEE/ABNT) EPS 1500...
2025-05-25 12:46:03 - callbacks.temperature_rise - INFO - Callbacks do módulo temperature_rise já registrados via decoradores @app.callback.
2025-05-25 12:46:03 - app - INFO - Callbacks registrados. Total: 50
2025-05-25 12:46:03 - callbacks.insulation_level_callbacks - INFO - Registrando callbacks de níveis de isolamento (APENAS OPÇÕES - V2)...
2025-05-25 12:46:03 - callbacks.insulation_level_callbacks - INFO - Callbacks de níveis de isolamento (APENAS OPÇÕES - V2) registrados.
2025-05-25 12:46:03 - callbacks.transformer_inputs - INFO - Registrando callbacks do módulo transformer_inputs para app Simulador de Testes de Transformadores (IEC/IEEE/ABNT) EPS 1500...
2025-05-25 12:46:03 - callbacks.temperature_rise - INFO - Registrando callbacks do módulo temperature_rise para app Simulador de Testes de Transformadores (IEC/IEEE/ABNT) EPS 1500...
2025-05-25 12:46:03 - callbacks.temperature_rise - INFO - Callbacks do módulo temperature_rise já registrados via decoradores @app.callback.
2025-05-25 12:46:03 - __main__ - INFO - Callbacks registrados. Total: 35
2025-05-25 12:46:03 - __main__ - INFO - Iniciando servidor Dash em http://127.0.0.1:8050/ (Debug: True)
2025-05-25 12:46:03 - dash.dash - INFO - Dash is running on http://127.0.0.1:8050/

2025-05-25 12:46:06 - callbacks.global_updates - DEBUG - Painel atualizado em 3.63ms
2025-05-25 12:46:06 - layouts.transformer_inputs - INFO - Criando layout Dados Básicos (v5 - Layout Geral e Pesos em 2 linhas)...
2025-05-25 12:46:06 - layouts.transformer_inputs - INFO - Classes de tensão carregadas para dropdown: 27 opções.
2025-05-25 12:46:07 - callbacks.global_updates - DEBUG - Painel atualizado em 0.0ms
2025-05-25 12:46:07 - callbacks.transformer_inputs - INFO - [LOAD TransformerInputs] Acionado por: None
2025-05-25 12:46:07 - callbacks.transformer_inputs - INFO - [LOAD TransformerInputs] Opções dos dropdowns ainda não foram populadas, aguardando...
2025-05-25 12:46:07 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_kv: None, Tensão: None, Path: /dados
2025-05-25 12:46:07 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT] Valores do MCP: NBI=None, SIL=None, TA=None, TI=None
2025-05-25 12:46:07 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT] Valores salvos: NBI=None, SIL=None, TA=None, TI=None
2025-05-25 12:46:07 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - AT] Um NÃO definido. Populando com opções GERAIS para Norma=IEC.
2025-05-25 12:46:07 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT-NEUTRO] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_Neutro: None, Conexão: estrela, Tensão: None
2025-05-25 12:46:07 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_kv: None, Tensão: None, Path: /dados
2025-05-25 12:46:07 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT-NEUTRO] Valores do MCP: NBI=None, SIL=None
2025-05-25 12:46:07 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT-NEUTRO] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_Neutro: None, Conexão: triangulo, Tensão: None
2025-05-25 12:46:07 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT] Valores do MCP: NBI=None, SIL=None, TA=None, TI=None
2025-05-25 12:46:07 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT-NEUTRO] Valores salvos: NBI=None, SIL=None
2025-05-25 12:46:07 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_kv: None, Tensão: None, Path: /dados
2025-05-25 12:46:07 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT-NEUTRO] Valores do MCP: NBI=None, SIL=None
2025-05-25 12:46:07 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT] Valores salvos: NBI=None, SIL=None, TA=None, TI=/dados
2025-05-25 12:46:07 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - AT-NEUTRO] Um_Neutro NÃO definido. Populando com opções GERAIS para Norma=IEC.
2025-05-25 12:46:07 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO-NEUTRO] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_Neutro: None, Conexão:  , Tensão: None
2025-05-25 12:46:07 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO] Valores do MCP: NBI=None, SIL=None, TA=None, TI=None
2025-05-25 12:46:07 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT-NEUTRO] Valores salvos: NBI=None, SIL=None
2025-05-25 12:46:07 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - BT] Um NÃO definido. Populando com opções GERAIS para Norma=IEC.
2025-05-25 12:46:07 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO-NEUTRO] Valores do MCP: NBI=None, SIL=None
2025-05-25 12:46:07 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO] Valores salvos: NBI=None, SIL=None, TA=None, TI=/dados
2025-05-25 12:46:07 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO-NEUTRO] Valores salvos: NBI=None, SIL=None
2025-05-25 12:46:07 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - TERCIARIO] Um NÃO definido. Populando com opções GERAIS para Norma=IEC.
2025-05-25 12:50:41 - callbacks.temperature_rise - INFO - Registrando callbacks do módulo temperature_rise para app Simulador de Testes de Transformadores (IEC/IEEE/ABNT) EPS 1500...
2025-05-25 12:50:41 - callbacks.temperature_rise - INFO - Callbacks do módulo temperature_rise já registrados via decoradores @app.callback.
2025-05-25 12:50:41 - app - INFO - Callbacks registrados. Total: 50
2025-05-25 12:50:41 - callbacks.insulation_level_callbacks - INFO - Registrando callbacks de níveis de isolamento (APENAS OPÇÕES - V2)...
2025-05-25 12:50:41 - callbacks.insulation_level_callbacks - INFO - Callbacks de níveis de isolamento (APENAS OPÇÕES - V2) registrados.
2025-05-25 12:50:41 - callbacks.transformer_inputs - INFO - Registrando callbacks do módulo transformer_inputs para app Simulador de Testes de Transformadores (IEC/IEEE/ABNT) EPS 1500...
2025-05-25 12:50:41 - callbacks.temperature_rise - INFO - Registrando callbacks do módulo temperature_rise para app Simulador de Testes de Transformadores (IEC/IEEE/ABNT) EPS 1500...
2025-05-25 12:50:41 - callbacks.temperature_rise - INFO - Callbacks do módulo temperature_rise já registrados via decoradores @app.callback.
2025-05-25 12:50:41 - __main__ - INFO - Callbacks registrados. Total: 35
2025-05-25 12:50:41 - __main__ - INFO - Iniciando servidor Dash em http://127.0.0.1:8050/ (Debug: True)
2025-05-25 12:50:41 - dash.dash - INFO - Dash is running on http://127.0.0.1:8050/

2025-05-25 12:50:44 - layouts.transformer_inputs - INFO - Criando layout Dados Básicos (v5 - Layout Geral e Pesos em 2 linhas)...
2025-05-25 12:50:44 - layouts.transformer_inputs - INFO - Classes de tensão carregadas para dropdown: 27 opções.
2025-05-25 12:50:44 - callbacks.global_updates - DEBUG - Painel atualizado em 1.0ms
2025-05-25 12:50:44 - callbacks.global_updates - DEBUG - Painel atualizado em 0.0ms
2025-05-25 12:50:44 - callbacks.transformer_inputs - INFO - [LOAD TransformerInputs] Acionado por: None
2025-05-25 12:50:44 - callbacks.transformer_inputs - INFO - [LOAD TransformerInputs] Opções dos dropdowns ainda não foram populadas, aguardando...
2025-05-25 12:50:44 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_kv: None, Tensão: None, Path: /dados
2025-05-25 12:50:44 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT-NEUTRO] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_Neutro: None, Conexão: estrela, Tensão: None
2025-05-25 12:50:44 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_kv: None, Tensão: None, Path: /dados
2025-05-25 12:50:44 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT] Valores do MCP: NBI=None, SIL=None, TA=None, TI=None
2025-05-25 12:50:44 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT-NEUTRO] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_Neutro: None, Conexão: triangulo, Tensão: None
2025-05-25 12:50:44 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT-NEUTRO] Valores do MCP: NBI=None, SIL=None
2025-05-25 12:50:44 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT] Valores do MCP: NBI=None, SIL=None, TA=None, TI=None
2025-05-25 12:50:44 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_kv: None, Tensão: None, Path: /dados
2025-05-25 12:50:44 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT] Valores salvos: NBI=None, SIL=None, TA=None, TI=None
2025-05-25 12:50:44 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT-NEUTRO] Valores do MCP: NBI=None, SIL=None
2025-05-25 12:50:44 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT-NEUTRO] Valores salvos: NBI=None, SIL=None
2025-05-25 12:50:44 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO-NEUTRO] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_Neutro: None, Conexão:  , Tensão: None
2025-05-25 12:50:44 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT] Valores salvos: NBI=None, SIL=None, TA=None, TI=/dados
2025-05-25 12:50:44 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO] Valores do MCP: NBI=None, SIL=None, TA=None, TI=None
2025-05-25 12:50:44 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - AT] Um NÃO definido. Populando com opções GERAIS para Norma=IEC.
2025-05-25 12:50:44 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT-NEUTRO] Valores salvos: NBI=None, SIL=None
2025-05-25 12:50:44 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - AT-NEUTRO] Um_Neutro NÃO definido. Populando com opções GERAIS para Norma=IEC.
2025-05-25 12:50:44 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO-NEUTRO] Valores do MCP: NBI=None, SIL=None
2025-05-25 12:50:44 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - BT] Um NÃO definido. Populando com opções GERAIS para Norma=IEC.
2025-05-25 12:50:44 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO] Valores salvos: NBI=None, SIL=None, TA=None, TI=/dados
2025-05-25 12:50:44 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO-NEUTRO] Valores salvos: NBI=None, SIL=None
2025-05-25 12:50:44 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - TERCIARIO] Um NÃO definido. Populando com opções GERAIS para Norma=IEC.
2025-05-25 12:51:01 - layouts.transformer_inputs - INFO - Criando layout Dados Básicos (v5 - Layout Geral e Pesos em 2 linhas)...
2025-05-25 12:51:01 - layouts.transformer_inputs - INFO - Classes de tensão carregadas para dropdown: 27 opções.
2025-05-25 12:51:01 - callbacks.global_updates - DEBUG - Painel atualizado em 1.4ms
2025-05-25 12:51:01 - callbacks.global_updates - DEBUG - Painel atualizado em 0.0ms
2025-05-25 12:51:01 - callbacks.transformer_inputs - INFO - [LOAD TransformerInputs] Acionado por: None
2025-05-25 12:51:01 - callbacks.transformer_inputs - INFO - [LOAD TransformerInputs] Opções dos dropdowns ainda não foram populadas, aguardando...
2025-05-25 12:51:01 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_kv: None, Tensão: None, Path: /dados
2025-05-25 12:51:01 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT-NEUTRO] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_Neutro: None, Conexão: estrela, Tensão: None
2025-05-25 12:51:01 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT] Valores do MCP: NBI=None, SIL=None, TA=None, TI=None
2025-05-25 12:51:01 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_kv: None, Tensão: None, Path: /dados
2025-05-25 12:51:01 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT-NEUTRO] Valores do MCP: NBI=None, SIL=None
2025-05-25 12:51:01 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT] Valores salvos: NBI=None, SIL=None, TA=None, TI=None
2025-05-25 12:51:01 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT-NEUTRO] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_Neutro: None, Conexão: triangulo, Tensão: None
2025-05-25 12:51:01 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT] Valores do MCP: NBI=None, SIL=None, TA=None, TI=None
2025-05-25 12:51:01 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT-NEUTRO] Valores salvos: NBI=None, SIL=None
2025-05-25 12:51:01 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - AT] Um NÃO definido. Populando com opções GERAIS para Norma=IEC.
2025-05-25 12:51:01 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_kv: None, Tensão: None, Path: /dados
2025-05-25 12:51:01 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT-NEUTRO] Valores do MCP: NBI=None, SIL=None
2025-05-25 12:51:01 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO-NEUTRO] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_Neutro: None, Conexão:  , Tensão: None
2025-05-25 12:51:01 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT] Valores salvos: NBI=None, SIL=None, TA=None, TI=/dados
2025-05-25 12:51:01 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - AT-NEUTRO] Um_Neutro NÃO definido. Populando com opções GERAIS para Norma=IEC.
2025-05-25 12:51:01 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO] Valores do MCP: NBI=None, SIL=None, TA=None, TI=None
2025-05-25 12:51:01 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT-NEUTRO] Valores salvos: NBI=None, SIL=None
2025-05-25 12:51:01 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO-NEUTRO] Valores do MCP: NBI=None, SIL=None
2025-05-25 12:51:01 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - BT] Um NÃO definido. Populando com opções GERAIS para Norma=IEC.
2025-05-25 12:51:01 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO] Valores salvos: NBI=None, SIL=None, TA=None, TI=/dados
2025-05-25 12:51:01 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO-NEUTRO] Valores salvos: NBI=None, SIL=None
2025-05-25 12:51:01 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - TERCIARIO] Um NÃO definido. Populando com opções GERAIS para Norma=IEC.
2025-05-25 12:52:25 - callbacks.temperature_rise - INFO - Registrando callbacks do módulo temperature_rise para app Simulador de Testes de Transformadores (IEC/IEEE/ABNT) EPS 1500...
2025-05-25 12:52:25 - callbacks.temperature_rise - INFO - Callbacks do módulo temperature_rise já registrados via decoradores @app.callback.
2025-05-25 12:52:25 - app - INFO - Callbacks registrados. Total: 50
2025-05-25 12:52:25 - callbacks.insulation_level_callbacks - INFO - Registrando callbacks de níveis de isolamento (APENAS OPÇÕES - V2)...
2025-05-25 12:52:25 - callbacks.insulation_level_callbacks - INFO - Callbacks de níveis de isolamento (APENAS OPÇÕES - V2) registrados.
2025-05-25 12:52:25 - callbacks.transformer_inputs - INFO - Registrando callbacks do módulo transformer_inputs para app Simulador de Testes de Transformadores (IEC/IEEE/ABNT) EPS 1500...
2025-05-25 12:52:25 - callbacks.temperature_rise - INFO - Registrando callbacks do módulo temperature_rise para app Simulador de Testes de Transformadores (IEC/IEEE/ABNT) EPS 1500...
2025-05-25 12:52:25 - callbacks.temperature_rise - INFO - Callbacks do módulo temperature_rise já registrados via decoradores @app.callback.
2025-05-25 12:52:25 - __main__ - INFO - Callbacks registrados. Total: 35
2025-05-25 12:52:25 - __main__ - INFO - Iniciando servidor Dash em http://127.0.0.1:8050/ (Debug: True)
2025-05-25 12:52:25 - dash.dash - INFO - Dash is running on http://127.0.0.1:8050/

2025-05-25 13:15:04 - callbacks.temperature_rise - INFO - Registrando callbacks do módulo temperature_rise para app Simulador de Testes de Transformadores (IEC/IEEE/ABNT) EPS 1500...
2025-05-25 13:15:04 - callbacks.temperature_rise - INFO - Callbacks do módulo temperature_rise já registrados via decoradores @app.callback.
2025-05-25 13:15:04 - app - INFO - Callbacks registrados. Total: 50
2025-05-25 13:15:04 - callbacks.insulation_level_callbacks - INFO - Registrando callbacks de níveis de isolamento (APENAS OPÇÕES - V2)...
2025-05-25 13:15:04 - callbacks.insulation_level_callbacks - INFO - Callbacks de níveis de isolamento (APENAS OPÇÕES - V2) registrados.
2025-05-25 13:15:04 - callbacks.transformer_inputs - INFO - Registrando callbacks do módulo transformer_inputs para app Simulador de Testes de Transformadores (IEC/IEEE/ABNT) EPS 1500...
2025-05-25 13:15:04 - callbacks.temperature_rise - INFO - Registrando callbacks do módulo temperature_rise para app Simulador de Testes de Transformadores (IEC/IEEE/ABNT) EPS 1500...
2025-05-25 13:15:04 - callbacks.temperature_rise - INFO - Callbacks do módulo temperature_rise já registrados via decoradores @app.callback.
2025-05-25 13:15:04 - __main__ - INFO - Callbacks registrados. Total: 35
2025-05-25 13:15:04 - __main__ - INFO - Iniciando servidor Dash em http://127.0.0.1:8050/ (Debug: True)
2025-05-25 13:15:04 - dash.dash - INFO - Dash is running on http://127.0.0.1:8050/

2025-05-25 13:15:07 - callbacks.global_updates - WARNING - Dados essenciais ausentes no transformer_store_data
2025-05-25 13:15:07 - components.transformer_info_template - WARNING - transformer_data inválido ou vazio: {}
2025-05-25 13:15:07 - callbacks.global_updates - DEBUG - Painel atualizado em 2.0ms
2025-05-25 13:15:07 - layouts.transformer_inputs - INFO - Criando layout Dados Básicos (v5 - Layout Geral e Pesos em 2 linhas)...
2025-05-25 13:15:07 - layouts.transformer_inputs - INFO - Classes de tensão carregadas para dropdown: 27 opções.
2025-05-25 13:15:07 - callbacks.global_updates - WARNING - Dados essenciais ausentes no transformer_store_data
2025-05-25 13:15:07 - components.transformer_info_template - WARNING - transformer_data inválido ou vazio: {}
2025-05-25 13:15:07 - callbacks.global_updates - DEBUG - Painel atualizado em 1.0ms
2025-05-25 13:15:07 - callbacks.transformer_inputs - INFO - [LOAD TransformerInputs] Acionado por: None
2025-05-25 13:15:07 - callbacks.transformer_inputs - INFO - [LOAD TransformerInputs] Opções dos dropdowns ainda não foram populadas, aguardando...
2025-05-25 13:15:07 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_kv: 69, Tensão: 69, Path: /dados
2025-05-25 13:15:07 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT-NEUTRO] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_Neutro: None, Conexão: triangulo, Tensão: 69
2025-05-25 13:15:07 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_kv: 3.6, Tensão: 13.8, Path: /dados
2025-05-25 13:15:07 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT] Valores do MCP: NBI=None, SIL=None, TA=None, TI=None
2025-05-25 13:15:07 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT-NEUTRO] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_Neutro: None, Conexão: triangulo, Tensão: 13.8
2025-05-25 13:15:07 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT-NEUTRO] Valores do MCP: NBI=None, SIL=None
2025-05-25 13:15:07 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_kv: 24, Tensão: 13, Path: /dados
2025-05-25 13:15:07 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT] Valores do MCP: NBI=None, SIL=None, TA=None, TI=None
2025-05-25 13:15:07 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT] Valores salvos: NBI=None, SIL=None, TA=None, TI=
2025-05-25 13:15:07 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO-NEUTRO] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_Neutro: None, Conexão: triangulo, Tensão: 13
2025-05-25 13:15:07 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT-NEUTRO] Valores do MCP: NBI=None, SIL=None
2025-05-25 13:15:07 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT-NEUTRO] Valores salvos: NBI=None, SIL=None
2025-05-25 13:15:07 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO] Valores do MCP: NBI=None, SIL=None, TA=None, TI=None
2025-05-25 13:15:07 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT] Valores salvos: NBI=None, SIL=None, TA=None, TI=/dados
2025-05-25 13:15:07 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO-NEUTRO] Valores do MCP: NBI=None, SIL=None
2025-05-25 13:15:07 - app_core.isolation_repo - WARNING - [ISOLATION] Nenhum registro encontrado para Um=69.0kV e norma=IEC/NBR. Usando valores padrão.
2025-05-25 13:15:07 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT-NEUTRO] Valores salvos: NBI=None, SIL=None
2025-05-25 13:15:07 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO] Valores salvos: NBI=None, SIL=None, TA=None, TI=/dados
2025-05-25 13:15:07 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO-NEUTRO] Valores salvos: NBI=None, SIL=None
2025-05-25 13:15:07 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - AT] Opções FILTRADAS por Um=69.0 e Norma=IEC.
2025-05-25 13:15:07 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - BT] Opções FILTRADAS por Um=3.6 e Norma=IEC.
2025-05-25 13:15:07 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - TERCIARIO] Opções FILTRADAS por Um=24.0 e Norma=IEC.
2025-05-25 13:15:57 - callbacks.transformer_inputs - INFO - [LOAD TransformerInputs] Acionado por: url
2025-05-25 13:15:57 - callbacks.transformer_inputs - DEBUG - [LOAD TransformerInputs] Não estamos na página de dados (pathname=/perdas, clean_path=perdas), prevenindo atualização
2025-05-25 13:15:57 - utils.mcp_persistence - INFO - [ensure_mcp_data_propagation] Store de origem transformer-inputs-store É a fonte-de-verdade para campos básicos
2025-05-25 13:15:57 - app_core.transformer_mcp - DEBUG - [MCP SET] Dados definidos para store 'losses-store'.
2025-05-25 13:15:57 - utils.mcp_persistence - INFO - [ensure_mcp_data_propagation] Dados básicos propagados para losses-store
2025-05-25 13:15:57 - layouts.losses - INFO - Creating Losses layout...
2025-05-25 13:15:57 - layouts.losses - INFO - [Losses Layout] Dados do transformador obtidos do losses-store.
2025-05-25 13:15:57 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT] Acionado por: url. Norma: IEC, Um_kv: 69, Tensão: 69, Path: /perdas
2025-05-25 13:15:57 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT] Valores do MCP: NBI=None, SIL=None, TA=None, TI=None
2025-05-25 13:15:57 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT] Acionado por: url. Norma: IEC, Um_kv: 3.6, Tensão: 13.8, Path: /perdas
2025-05-25 13:15:57 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT] Valores salvos: NBI=None, SIL=None, TA=None, TI=
2025-05-25 13:15:57 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO] Acionado por: url. Norma: IEC, Um_kv: 24, Tensão: 13, Path: /perdas
2025-05-25 13:15:57 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT] Valores do MCP: NBI=None, SIL=None, TA=None, TI=None
2025-05-25 13:15:57 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT-NEUTRO] Acionado por: url. Norma: IEC, Um_Neutro: None, Conexão: triangulo, Tensão: 69
2025-05-25 13:15:57 - app_core.isolation_repo - WARNING - [ISOLATION] Nenhum registro encontrado para Um=69.0kV e norma=IEC/NBR. Usando valores padrão.
2025-05-25 13:15:57 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO] Valores do MCP: NBI=None, SIL=None, TA=None, TI=None
2025-05-25 13:15:57 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT-NEUTRO] Acionado por: url. Norma: IEC, Um_Neutro: None, Conexão: triangulo, Tensão: 13.8
2025-05-25 13:15:57 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT] Valores salvos: NBI=None, SIL=None, TA=None, TI=/perdas
2025-05-25 13:15:57 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO-NEUTRO] Acionado por: url. Norma: IEC, Um_Neutro: None, Conexão: triangulo, Tensão: 13
2025-05-25 13:15:57 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT-NEUTRO] Valores do MCP: NBI=None, SIL=None
2025-05-25 13:15:57 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - AT] Opções FILTRADAS por Um=69.0 e Norma=IEC.
2025-05-25 13:15:57 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO] Valores salvos: NBI=None, SIL=None, TA=None, TI=/perdas
2025-05-25 13:15:57 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT-NEUTRO] Valores do MCP: NBI=None, SIL=None
2025-05-25 13:15:57 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO-NEUTRO] Valores do MCP: NBI=None, SIL=None
2025-05-25 13:15:57 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT-NEUTRO] Valores salvos: NBI=None, SIL=None
2025-05-25 13:15:57 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - BT] Opções FILTRADAS por Um=3.6 e Norma=IEC.
2025-05-25 13:15:57 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT-NEUTRO] Valores salvos: NBI=None, SIL=None
2025-05-25 13:15:57 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO-NEUTRO] Valores salvos: NBI=None, SIL=None
2025-05-25 13:15:57 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - TERCIARIO] Opções FILTRADAS por Um=24.0 e Norma=IEC.
2025-05-25 13:15:57 - callbacks.global_updates - WARNING - Dados essenciais ausentes no transformer_store_data
2025-05-25 13:15:57 - components.transformer_info_template - WARNING - transformer_data inválido ou vazio: {}
2025-05-25 13:15:57 - callbacks.global_updates - DEBUG - Painel atualizado em 3.24ms
2025-05-25 13:15:57 - callbacks.losses - CRITICAL - [LOSSES POPULATE VAZIO] Acionado. Aba: tab-vazio, Trigger: N/A
2025-05-25 13:16:13 - callbacks.losses - CRITICAL - [LOSSES POPULATE VAZIO] Acionado. Aba: tab-carga, Trigger: tabs-perdas
2025-05-25 13:16:13 - callbacks.losses - CRITICAL - [LOSSES POPULATE CARGA] Acionado. Aba: tab-carga, Trigger: N/A
2025-05-25 13:16:13 - callbacks.losses - CRITICAL - [LOSSES POPULATE CARGA] Acionado. Aba: tab-vazio, Trigger: tabs-perdas
2025-05-25 13:16:13 - callbacks.losses - CRITICAL - [LOSSES POPULATE VAZIO] Acionado. Aba: tab-vazio, Trigger: N/A
2025-05-25 13:16:14 - callbacks.losses - CRITICAL - [LOSSES POPULATE VAZIO] Acionado. Aba: tab-carga, Trigger: tabs-perdas
2025-05-25 13:16:14 - callbacks.losses - CRITICAL - [LOSSES POPULATE CARGA] Acionado. Aba: tab-carga, Trigger: N/A
2025-05-25 13:16:17 - layouts.transformer_inputs - INFO - Criando layout Dados Básicos (v5 - Layout Geral e Pesos em 2 linhas)...
2025-05-25 13:16:17 - layouts.transformer_inputs - INFO - Classes de tensão carregadas para dropdown: 27 opções.
2025-05-25 13:16:17 - callbacks.global_updates - WARNING - Dados essenciais ausentes no transformer_store_data
2025-05-25 13:16:17 - components.transformer_info_template - WARNING - transformer_data inválido ou vazio: {}
2025-05-25 13:16:17 - callbacks.global_updates - DEBUG - Painel atualizado em 1.0ms
2025-05-25 13:16:17 - callbacks.transformer_inputs - INFO - [LOAD TransformerInputs] Acionado por: None
2025-05-25 13:16:17 - callbacks.transformer_inputs - INFO - [LOAD TransformerInputs] Opções dos dropdowns ainda não foram populadas, aguardando...
2025-05-25 13:16:17 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_kv: 69, Tensão: 69, Path: /dados
2025-05-25 13:16:17 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT] Valores do MCP: NBI=None, SIL=None, TA=None, TI=None
2025-05-25 13:16:17 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT-NEUTRO] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_Neutro: None, Conexão: triangulo, Tensão: 69
2025-05-25 13:16:17 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT] Valores salvos: NBI=None, SIL=None, TA=None, TI=
2025-05-25 13:16:17 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_kv: 3.6, Tensão: 13.8, Path: /dados
2025-05-25 13:16:17 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT-NEUTRO] Valores do MCP: NBI=None, SIL=None
2025-05-25 13:16:17 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT-NEUTRO] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_Neutro: None, Conexão: triangulo, Tensão: 13.8
2025-05-25 13:16:17 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT] Valores do MCP: NBI=None, SIL=None, TA=None, TI=None
2025-05-25 13:16:17 - app_core.isolation_repo - WARNING - [ISOLATION] Nenhum registro encontrado para Um=69.0kV e norma=IEC/NBR. Usando valores padrão.
2025-05-25 13:16:17 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_kv: 24, Tensão: 13, Path: /dados
2025-05-25 13:16:17 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT-NEUTRO] Valores salvos: NBI=None, SIL=None
2025-05-25 13:16:17 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT-NEUTRO] Valores do MCP: NBI=None, SIL=None
2025-05-25 13:16:17 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO-NEUTRO] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_Neutro: None, Conexão: triangulo, Tensão: 13
2025-05-25 13:16:17 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT] Valores salvos: NBI=None, SIL=None, TA=None, TI=/dados
2025-05-25 13:16:17 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - AT] Opções FILTRADAS por Um=69.0 e Norma=IEC.
2025-05-25 13:16:17 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO] Valores do MCP: NBI=None, SIL=None, TA=None, TI=None
2025-05-25 13:16:17 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT-NEUTRO] Valores salvos: NBI=None, SIL=None
2025-05-25 13:16:17 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO-NEUTRO] Valores do MCP: NBI=None, SIL=None
2025-05-25 13:16:17 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO] Valores salvos: NBI=None, SIL=None, TA=None, TI=/dados
2025-05-25 13:16:17 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO-NEUTRO] Valores salvos: NBI=None, SIL=None
2025-05-25 13:16:17 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - BT] Opções FILTRADAS por Um=3.6 e Norma=IEC.
2025-05-25 13:16:17 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - TERCIARIO] Opções FILTRADAS por Um=24.0 e Norma=IEC.
2025-05-25 13:16:19 - callbacks.transformer_inputs - INFO - [LOAD TransformerInputs] Acionado por: url
2025-05-25 13:16:19 - callbacks.transformer_inputs - DEBUG - [LOAD TransformerInputs] Não estamos na página de dados (pathname=/analise-dieletrica, clean_path=analise-dieletrica), prevenindo atualização
2025-05-25 13:16:19 - layouts.dieletric_analysis - INFO - Creating Dielectric Analysis layout (from dieletric_analysis.py)...
2025-05-25 13:16:19 - app_core.standards - ERROR - Arquivo JSON 'c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\tabela.json' não encontrado.
2025-05-25 13:16:19 - app_core.standards - ERROR - Arquivo JSON 'c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\tabela.json' não encontrado.
2025-05-25 13:16:19 - app_core.standards - ERROR - Arquivo JSON 'c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\tabela.json' não encontrado.
2025-05-25 13:16:19 - app_core.standards - ERROR - Arquivo JSON 'c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\tabela.json' não encontrado.
2025-05-25 13:16:19 - app_core.standards - CRITICAL - Falha CRÍTICA ao inicializar as classes das normas: Falha ao carregar dados JSON para NBR ou IEEE.
Traceback (most recent call last):
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\app_core\standards.py", line 613, in __init__
    raise RuntimeError("Falha ao carregar dados JSON para NBR ou IEEE.")
RuntimeError: Falha ao carregar dados JSON para NBR ou IEEE.
2025-05-25 13:16:19 - layouts.dieletric_analysis - WARNING - VerificadorTransformador is invalid upon creation in create_dielectric_layout.
2025-05-25 13:16:19 - components.ui_elements - WARNING - Verificador inválido ao criar coluna dielétrica (Alta Tensão).
2025-05-25 13:16:19 - components.ui_elements - WARNING - Verificador inválido ao criar coluna dielétrica (Baixa Tensão).
2025-05-25 13:16:19 - components.ui_elements - WARNING - Verificador inválido ao criar coluna dielétrica (Terciário).
2025-05-25 13:16:19 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT] Acionado por: url. Norma: IEC, Um_kv: 69, Tensão: 69, Path: /analise-dieletrica
2025-05-25 13:16:19 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT] Acionado por: url. Norma: IEC, Um_kv: 3.6, Tensão: 13.8, Path: /analise-dieletrica
2025-05-25 13:16:19 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT] Valores do MCP: NBI=None, SIL=None, TA=None, TI=None
2025-05-25 13:16:19 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT] Valores do MCP: NBI=None, SIL=None, TA=None, TI=None
2025-05-25 13:16:19 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO] Acionado por: url. Norma: IEC, Um_kv: 24, Tensão: 13, Path: /analise-dieletrica
2025-05-25 13:16:19 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT] Valores salvos: NBI=None, SIL=None, TA=None, TI=
2025-05-25 13:16:19 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT-NEUTRO] Acionado por: url. Norma: IEC, Um_Neutro: None, Conexão: triangulo, Tensão: 69
2025-05-25 13:16:19 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT-NEUTRO] Acionado por: url. Norma: IEC, Um_Neutro: None, Conexão: triangulo, Tensão: 13.8
2025-05-25 13:16:19 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT] Valores salvos: NBI=None, SIL=None, TA=None, TI=/analise-dieletrica
2025-05-25 13:16:19 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO] Valores do MCP: NBI=None, SIL=None, TA=None, TI=None
2025-05-25 13:16:19 - app_core.isolation_repo - WARNING - [ISOLATION] Nenhum registro encontrado para Um=69.0kV e norma=IEC/NBR. Usando valores padrão.
2025-05-25 13:16:19 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO-NEUTRO] Acionado por: url. Norma: IEC, Um_Neutro: None, Conexão: triangulo, Tensão: 13
2025-05-25 13:16:19 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT-NEUTRO] Valores do MCP: NBI=None, SIL=None
2025-05-25 13:16:19 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT-NEUTRO] Valores do MCP: NBI=None, SIL=None
2025-05-25 13:16:19 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO] Valores salvos: NBI=None, SIL=None, TA=None, TI=/analise-dieletrica
2025-05-25 13:16:19 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - AT] Opções FILTRADAS por Um=69.0 e Norma=IEC.
2025-05-25 13:16:19 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - BT] Opções FILTRADAS por Um=3.6 e Norma=IEC.
2025-05-25 13:16:19 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO-NEUTRO] Valores do MCP: NBI=None, SIL=None
2025-05-25 13:16:19 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT-NEUTRO] Valores salvos: NBI=None, SIL=None
2025-05-25 13:16:19 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT-NEUTRO] Valores salvos: NBI=None, SIL=None
2025-05-25 13:16:19 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - TERCIARIO] Opções FILTRADAS por Um=24.0 e Norma=IEC.
2025-05-25 13:16:19 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO-NEUTRO] Valores salvos: NBI=None, SIL=None
2025-05-25 13:16:19 - callbacks.global_updates - WARNING - Dados essenciais ausentes no transformer_store_data
2025-05-25 13:16:19 - components.transformer_info_template - WARNING - transformer_data inválido ou vazio: {}
2025-05-25 13:16:19 - callbacks.global_updates - DEBUG - Painel atualizado em 0.64ms
2025-05-25 13:16:19 - app_core.standards - ERROR - Arquivo JSON 'c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\tabela.json' não encontrado.
2025-05-25 13:16:19 - app_core.standards - ERROR - Arquivo JSON 'c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\tabela.json' não encontrado.
2025-05-25 13:16:19 - app_core.standards - ERROR - Arquivo JSON 'c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\tabela.json' não encontrado.
2025-05-25 13:16:19 - app_core.standards - ERROR - Arquivo JSON 'c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\tabela.json' não encontrado.
2025-05-25 13:16:19 - app_core.standards - CRITICAL - Falha CRÍTICA ao inicializar as classes das normas: Falha ao carregar dados JSON para NBR ou IEEE.
Traceback (most recent call last):
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\app_core\standards.py", line 613, in __init__
    raise RuntimeError("Falha ao carregar dados JSON para NBR ou IEEE.")
RuntimeError: Falha ao carregar dados JSON para NBR ou IEEE.
2025-05-25 13:16:19 - callbacks.dieletric_analysis - ERROR - Instância VerificadorTransformador criada, mas inválida (falha dados?).
2025-05-25 13:16:33 - layouts.transformer_inputs - INFO - Criando layout Dados Básicos (v5 - Layout Geral e Pesos em 2 linhas)...
2025-05-25 13:16:33 - layouts.transformer_inputs - INFO - Classes de tensão carregadas para dropdown: 27 opções.
2025-05-25 13:16:33 - callbacks.global_updates - WARNING - Dados essenciais ausentes no transformer_store_data
2025-05-25 13:16:33 - components.transformer_info_template - WARNING - transformer_data inválido ou vazio: {}
2025-05-25 13:16:33 - callbacks.global_updates - DEBUG - Painel atualizado em 1.0ms
2025-05-25 13:16:33 - callbacks.transformer_inputs - INFO - [LOAD TransformerInputs] Acionado por: None
2025-05-25 13:16:33 - callbacks.transformer_inputs - INFO - [LOAD TransformerInputs] Opções dos dropdowns ainda não foram populadas, aguardando...
2025-05-25 13:16:33 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_kv: 69, Tensão: 69, Path: /dados
2025-05-25 13:16:33 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT-NEUTRO] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_Neutro: None, Conexão: triangulo, Tensão: 69
2025-05-25 13:16:33 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT] Valores do MCP: NBI=None, SIL=None, TA=None, TI=None
2025-05-25 13:16:33 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_kv: 3.6, Tensão: 13.8, Path: /dados
2025-05-25 13:16:33 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT-NEUTRO] Valores do MCP: NBI=None, SIL=None
2025-05-25 13:16:33 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT] Valores salvos: NBI=None, SIL=None, TA=None, TI=
2025-05-25 13:16:33 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT-NEUTRO] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_Neutro: None, Conexão: triangulo, Tensão: 13.8
2025-05-25 13:16:33 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_kv: 24, Tensão: 13, Path: /dados
2025-05-25 13:16:33 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT] Valores do MCP: NBI=None, SIL=None, TA=None, TI=None
2025-05-25 13:16:33 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT-NEUTRO] Valores salvos: NBI=None, SIL=None
2025-05-25 13:16:33 - app_core.isolation_repo - WARNING - [ISOLATION] Nenhum registro encontrado para Um=69.0kV e norma=IEC/NBR. Usando valores padrão.
2025-05-25 13:16:33 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO-NEUTRO] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_Neutro: None, Conexão: triangulo, Tensão: 13
2025-05-25 13:16:33 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT-NEUTRO] Valores do MCP: NBI=None, SIL=None
2025-05-25 13:16:33 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO] Valores do MCP: NBI=None, SIL=None, TA=None, TI=None
2025-05-25 13:16:33 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT] Valores salvos: NBI=None, SIL=None, TA=None, TI=/dados
2025-05-25 13:16:33 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - AT] Opções FILTRADAS por Um=69.0 e Norma=IEC.
2025-05-25 13:16:33 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO-NEUTRO] Valores do MCP: NBI=None, SIL=None
2025-05-25 13:16:33 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT-NEUTRO] Valores salvos: NBI=None, SIL=None
2025-05-25 13:16:33 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO] Valores salvos: NBI=None, SIL=None, TA=None, TI=/dados
2025-05-25 13:16:33 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO-NEUTRO] Valores salvos: NBI=None, SIL=None
2025-05-25 13:16:33 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - BT] Opções FILTRADAS por Um=3.6 e Norma=IEC.
2025-05-25 13:16:33 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - TERCIARIO] Opções FILTRADAS por Um=24.0 e Norma=IEC.
2025-05-25 13:19:18 - callbacks.temperature_rise - INFO - Registrando callbacks do módulo temperature_rise para app Simulador de Testes de Transformadores (IEC/IEEE/ABNT) EPS 1500...
2025-05-25 13:19:18 - callbacks.temperature_rise - INFO - Callbacks do módulo temperature_rise já registrados via decoradores @app.callback.
2025-05-25 13:19:18 - app - INFO - Callbacks registrados. Total: 50
2025-05-25 13:19:18 - callbacks.insulation_level_callbacks - INFO - Registrando callbacks de níveis de isolamento (APENAS OPÇÕES - V2)...
2025-05-25 13:19:18 - callbacks.insulation_level_callbacks - INFO - Callbacks de níveis de isolamento (APENAS OPÇÕES - V2) registrados.
2025-05-25 13:19:18 - callbacks.transformer_inputs - INFO - Registrando callbacks do módulo transformer_inputs para app Simulador de Testes de Transformadores (IEC/IEEE/ABNT) EPS 1500...
2025-05-25 13:19:18 - callbacks.temperature_rise - INFO - Registrando callbacks do módulo temperature_rise para app Simulador de Testes de Transformadores (IEC/IEEE/ABNT) EPS 1500...
2025-05-25 13:19:18 - callbacks.temperature_rise - INFO - Callbacks do módulo temperature_rise já registrados via decoradores @app.callback.
2025-05-25 13:19:18 - __main__ - INFO - Callbacks registrados. Total: 35
2025-05-25 13:19:18 - __main__ - INFO - Iniciando servidor Dash em http://127.0.0.1:8050/ (Debug: True)
2025-05-25 13:19:18 - dash.dash - INFO - Dash is running on http://127.0.0.1:8050/

2025-05-25 13:19:20 - callbacks.global_updates - WARNING - Dados essenciais ausentes no transformer_store_data
2025-05-25 13:19:20 - components.transformer_info_template - WARNING - transformer_data inválido ou vazio: {}
2025-05-25 13:19:20 - layouts.transformer_inputs - INFO - Criando layout Dados Básicos (v5 - Layout Geral e Pesos em 2 linhas)...
2025-05-25 13:19:20 - callbacks.global_updates - DEBUG - Painel atualizado em 1.0ms
2025-05-25 13:19:20 - layouts.transformer_inputs - INFO - Classes de tensão carregadas para dropdown: 27 opções.
2025-05-25 13:19:20 - callbacks.global_updates - WARNING - Dados essenciais ausentes no transformer_store_data
2025-05-25 13:19:20 - components.transformer_info_template - WARNING - transformer_data inválido ou vazio: {}
2025-05-25 13:19:20 - callbacks.global_updates - DEBUG - Painel atualizado em 1.0ms
2025-05-25 13:19:20 - callbacks.transformer_inputs - INFO - [LOAD TransformerInputs] Acionado por: None
2025-05-25 13:19:20 - callbacks.transformer_inputs - INFO - [LOAD TransformerInputs] Opções dos dropdowns ainda não foram populadas, aguardando...
2025-05-25 13:19:20 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_kv: 69, Tensão: 69, Path: /dados
2025-05-25 13:19:20 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT-NEUTRO] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_Neutro: None, Conexão: triangulo, Tensão: 69
2025-05-25 13:19:20 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT] Valores do MCP: NBI=None, SIL=None, TA=None, TI=None
2025-05-25 13:19:20 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT-NEUTRO] Valores do MCP: NBI=None, SIL=None
2025-05-25 13:19:20 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_kv: 3.6, Tensão: 13.8, Path: /dados
2025-05-25 13:19:20 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT] Valores salvos: NBI=None, SIL=None, TA=None, TI=
2025-05-25 13:19:20 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT-NEUTRO] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_Neutro: None, Conexão: triangulo, Tensão: 13.8
2025-05-25 13:19:20 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT-NEUTRO] Valores salvos: NBI=None, SIL=None
2025-05-25 13:19:20 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT] Valores do MCP: NBI=None, SIL=None, TA=None, TI=None
2025-05-25 13:19:20 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_kv: 24, Tensão: 13, Path: /dados
2025-05-25 13:19:20 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT-NEUTRO] Valores do MCP: NBI=None, SIL=None
2025-05-25 13:19:20 - app_core.isolation_repo - WARNING - [ISOLATION] Nenhum registro encontrado para Um=69.0kV e norma=IEC/NBR. Usando valores padrão.
2025-05-25 13:19:20 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO-NEUTRO] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_Neutro: None, Conexão: triangulo, Tensão: 13
2025-05-25 13:19:20 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT] Valores salvos: NBI=None, SIL=None, TA=None, TI=/dados
2025-05-25 13:19:20 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO] Valores do MCP: NBI=None, SIL=None, TA=None, TI=None
2025-05-25 13:19:20 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT-NEUTRO] Valores salvos: NBI=None, SIL=None
2025-05-25 13:19:20 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - AT] Opções FILTRADAS por Um=69.0 e Norma=IEC.
2025-05-25 13:19:20 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO-NEUTRO] Valores do MCP: NBI=None, SIL=None
2025-05-25 13:19:20 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO] Valores salvos: NBI=None, SIL=None, TA=None, TI=/dados
2025-05-25 13:19:20 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - BT] Opções FILTRADAS por Um=3.6 e Norma=IEC.
2025-05-25 13:19:20 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO-NEUTRO] Valores salvos: NBI=None, SIL=None
2025-05-25 13:19:20 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - TERCIARIO] Opções FILTRADAS por Um=24.0 e Norma=IEC.
2025-05-25 13:20:37 - callbacks.temperature_rise - INFO - Registrando callbacks do módulo temperature_rise para app Simulador de Testes de Transformadores (IEC/IEEE/ABNT) EPS 1500...
2025-05-25 13:20:37 - callbacks.temperature_rise - INFO - Callbacks do módulo temperature_rise já registrados via decoradores @app.callback.
2025-05-25 13:20:37 - app - INFO - Callbacks registrados. Total: 50
2025-05-25 13:20:37 - callbacks.insulation_level_callbacks - INFO - Registrando callbacks de níveis de isolamento (APENAS OPÇÕES - V2)...
2025-05-25 13:20:37 - callbacks.insulation_level_callbacks - INFO - Callbacks de níveis de isolamento (APENAS OPÇÕES - V2) registrados.
2025-05-25 13:20:37 - callbacks.transformer_inputs - INFO - Registrando callbacks do módulo transformer_inputs para app Simulador de Testes de Transformadores (IEC/IEEE/ABNT) EPS 1500...
2025-05-25 13:20:37 - callbacks.temperature_rise - INFO - Registrando callbacks do módulo temperature_rise para app Simulador de Testes de Transformadores (IEC/IEEE/ABNT) EPS 1500...
2025-05-25 13:20:37 - callbacks.temperature_rise - INFO - Callbacks do módulo temperature_rise já registrados via decoradores @app.callback.
2025-05-25 13:20:37 - __main__ - INFO - Callbacks registrados. Total: 35
2025-05-25 13:20:37 - __main__ - INFO - Iniciando servidor Dash em http://127.0.0.1:8050/ (Debug: True)
2025-05-25 13:20:37 - dash.dash - INFO - Dash is running on http://127.0.0.1:8050/

2025-05-25 13:20:39 - callbacks.global_updates - WARNING - Dados essenciais ausentes no transformer_store_data
2025-05-25 13:20:39 - components.transformer_info_template - WARNING - transformer_data inválido ou vazio: {}
2025-05-25 13:20:39 - layouts.transformer_inputs - INFO - Criando layout Dados Básicos (v5 - Layout Geral e Pesos em 2 linhas)...
2025-05-25 13:20:39 - callbacks.global_updates - DEBUG - Painel atualizado em 1.0ms
2025-05-25 13:20:39 - layouts.transformer_inputs - INFO - Classes de tensão carregadas para dropdown: 27 opções.
2025-05-25 13:20:39 - callbacks.global_updates - WARNING - Dados essenciais ausentes no transformer_store_data
2025-05-25 13:20:39 - components.transformer_info_template - WARNING - transformer_data inválido ou vazio: {}
2025-05-25 13:20:39 - callbacks.transformer_inputs - INFO - [LOAD TransformerInputs] Acionado por: None
2025-05-25 13:20:39 - callbacks.global_updates - DEBUG - Painel atualizado em 1.0ms
2025-05-25 13:20:39 - callbacks.transformer_inputs - INFO - [LOAD TransformerInputs] Opções dos dropdowns ainda não foram populadas, aguardando...
2025-05-25 13:20:39 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_kv: 69, Tensão: 69, Path: /dados
2025-05-25 13:20:39 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT] Valores do MCP: NBI=None, SIL=None, TA=None, TI=None
2025-05-25 13:20:39 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT] Valores salvos: NBI=None, SIL=None, TA=None, TI=
2025-05-25 13:20:39 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_kv: 3.6, Tensão: 13.8, Path: /dados
2025-05-25 13:20:39 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT-NEUTRO] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_Neutro: None, Conexão: triangulo, Tensão: 13.8
2025-05-25 13:20:39 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT-NEUTRO] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_Neutro: None, Conexão: triangulo, Tensão: 69
2025-05-25 13:20:39 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT] Valores do MCP: NBI=None, SIL=None, TA=None, TI=None
2025-05-25 13:20:39 - app_core.isolation_repo - WARNING - [ISOLATION] Nenhum registro encontrado para Um=69.0kV e norma=IEC/NBR. Usando valores padrão.
2025-05-25 13:20:39 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT-NEUTRO] Valores do MCP: NBI=None, SIL=None
2025-05-25 13:20:39 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_kv: 24, Tensão: 13, Path: /dados
2025-05-25 13:20:39 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT-NEUTRO] Valores do MCP: NBI=None, SIL=None
2025-05-25 13:20:39 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO-NEUTRO] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_Neutro: None, Conexão: triangulo, Tensão: 13
2025-05-25 13:20:39 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT] Valores salvos: NBI=None, SIL=None, TA=None, TI=/dados
2025-05-25 13:20:39 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - AT] Opções FILTRADAS por Um=69.0 e Norma=IEC.
2025-05-25 13:20:39 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT-NEUTRO] Valores salvos: NBI=None, SIL=None
2025-05-25 13:20:39 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO] Valores do MCP: NBI=None, SIL=None, TA=None, TI=None
2025-05-25 13:20:39 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT-NEUTRO] Valores salvos: NBI=None, SIL=None
2025-05-25 13:20:39 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO-NEUTRO] Valores do MCP: NBI=None, SIL=None
2025-05-25 13:20:39 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO] Valores salvos: NBI=None, SIL=None, TA=None, TI=/dados
2025-05-25 13:20:39 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - BT] Opções FILTRADAS por Um=3.6 e Norma=IEC.
2025-05-25 13:20:39 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO-NEUTRO] Valores salvos: NBI=None, SIL=None
2025-05-25 13:20:39 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - TERCIARIO] Opções FILTRADAS por Um=24.0 e Norma=IEC.
2025-05-25 13:20:42 - callbacks.global_updates - WARNING - Dados essenciais ausentes no transformer_store_data
2025-05-25 13:20:42 - components.transformer_info_template - WARNING - transformer_data inválido ou vazio: {}
2025-05-25 13:20:42 - callbacks.global_updates - DEBUG - Painel atualizado em 1.0ms
2025-05-25 13:20:42 - layouts.transformer_inputs - INFO - Criando layout Dados Básicos (v5 - Layout Geral e Pesos em 2 linhas)...
2025-05-25 13:20:42 - layouts.transformer_inputs - INFO - Classes de tensão carregadas para dropdown: 27 opções.
2025-05-25 13:20:42 - callbacks.global_updates - WARNING - Dados essenciais ausentes no transformer_store_data
2025-05-25 13:20:42 - components.transformer_info_template - WARNING - transformer_data inválido ou vazio: {}
2025-05-25 13:20:42 - callbacks.global_updates - DEBUG - Painel atualizado em 1.0ms
2025-05-25 13:20:42 - callbacks.transformer_inputs - INFO - [LOAD TransformerInputs] Acionado por: None
2025-05-25 13:20:42 - callbacks.transformer_inputs - INFO - [LOAD TransformerInputs] Opções dos dropdowns ainda não foram populadas, aguardando...
2025-05-25 13:20:42 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_kv: 69, Tensão: 69, Path: /dados
2025-05-25 13:20:42 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT-NEUTRO] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_Neutro: None, Conexão: triangulo, Tensão: 69
2025-05-25 13:20:42 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT] Valores do MCP: NBI=None, SIL=None, TA=None, TI=None
2025-05-25 13:20:42 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_kv: 3.6, Tensão: 13.8, Path: /dados
2025-05-25 13:20:42 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT-NEUTRO] Valores do MCP: NBI=None, SIL=None
2025-05-25 13:20:42 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT] Valores salvos: NBI=None, SIL=None, TA=None, TI=
2025-05-25 13:20:42 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT-NEUTRO] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_Neutro: None, Conexão: triangulo, Tensão: 13.8
2025-05-25 13:20:42 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT] Valores do MCP: NBI=None, SIL=None, TA=None, TI=None
2025-05-25 13:20:42 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_kv: 24, Tensão: 13, Path: /dados
2025-05-25 13:20:42 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT-NEUTRO] Valores salvos: NBI=None, SIL=None
2025-05-25 13:20:42 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO-NEUTRO] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_Neutro: None, Conexão: triangulo, Tensão: 13
2025-05-25 13:20:42 - app_core.isolation_repo - WARNING - [ISOLATION] Nenhum registro encontrado para Um=69.0kV e norma=IEC/NBR. Usando valores padrão.
2025-05-25 13:20:42 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT] Valores salvos: NBI=None, SIL=None, TA=None, TI=/dados
2025-05-25 13:20:42 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT-NEUTRO] Valores do MCP: NBI=None, SIL=None
2025-05-25 13:20:42 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO] Valores do MCP: NBI=None, SIL=None, TA=None, TI=None
2025-05-25 13:20:42 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO-NEUTRO] Valores do MCP: NBI=None, SIL=None
2025-05-25 13:20:42 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - AT] Opções FILTRADAS por Um=69.0 e Norma=IEC.
2025-05-25 13:20:42 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT-NEUTRO] Valores salvos: NBI=None, SIL=None
2025-05-25 13:20:42 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO] Valores salvos: NBI=None, SIL=None, TA=None, TI=/dados
2025-05-25 13:20:42 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - BT] Opções FILTRADAS por Um=3.6 e Norma=IEC.
2025-05-25 13:20:42 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO-NEUTRO] Valores salvos: NBI=None, SIL=None
2025-05-25 13:20:42 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - TERCIARIO] Opções FILTRADAS por Um=24.0 e Norma=IEC.
2025-05-25 13:20:51 - callbacks.transformer_inputs - INFO - [LOAD TransformerInputs] Acionado por: url
2025-05-25 13:20:51 - callbacks.transformer_inputs - DEBUG - [LOAD TransformerInputs] Não estamos na página de dados (pathname=/perdas, clean_path=perdas), prevenindo atualização
2025-05-25 13:20:51 - utils.mcp_persistence - INFO - [ensure_mcp_data_propagation] Store de origem transformer-inputs-store É a fonte-de-verdade para campos básicos
2025-05-25 13:20:51 - app_core.transformer_mcp - DEBUG - [MCP SET] Dados definidos para store 'losses-store'.
2025-05-25 13:20:51 - utils.mcp_persistence - INFO - [ensure_mcp_data_propagation] Dados básicos propagados para losses-store
2025-05-25 13:20:51 - layouts.losses - INFO - Creating Losses layout...
2025-05-25 13:20:51 - layouts.losses - INFO - [Losses Layout] Dados do transformador obtidos do losses-store.
2025-05-25 13:20:51 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT] Acionado por: url. Norma: IEC, Um_kv: 69, Tensão: 69, Path: /perdas
2025-05-25 13:20:51 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT] Acionado por: url. Norma: IEC, Um_kv: 3.6, Tensão: 13.8, Path: /perdas
2025-05-25 13:20:51 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT] Valores do MCP: NBI=None, SIL=None, TA=None, TI=None
2025-05-25 13:20:51 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT] Valores do MCP: NBI=None, SIL=None, TA=None, TI=None
2025-05-25 13:20:51 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO] Acionado por: url. Norma: IEC, Um_kv: 24, Tensão: 13, Path: /perdas
2025-05-25 13:20:51 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT] Valores salvos: NBI=None, SIL=None, TA=None, TI=
2025-05-25 13:20:51 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT-NEUTRO] Acionado por: url. Norma: IEC, Um_Neutro: None, Conexão: triangulo, Tensão: 69
2025-05-25 13:20:51 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT] Valores salvos: NBI=None, SIL=None, TA=None, TI=/perdas
2025-05-25 13:20:51 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO] Valores do MCP: NBI=None, SIL=None, TA=None, TI=None
2025-05-25 13:20:51 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT-NEUTRO] Acionado por: url. Norma: IEC, Um_Neutro: None, Conexão: triangulo, Tensão: 13.8
2025-05-25 13:20:51 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT-NEUTRO] Valores do MCP: NBI=None, SIL=None
2025-05-25 13:20:51 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO-NEUTRO] Acionado por: url. Norma: IEC, Um_Neutro: None, Conexão: triangulo, Tensão: 13
2025-05-25 13:20:51 - app_core.isolation_repo - WARNING - [ISOLATION] Nenhum registro encontrado para Um=69.0kV e norma=IEC/NBR. Usando valores padrão.
2025-05-25 13:20:51 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO] Valores salvos: NBI=None, SIL=None, TA=None, TI=/perdas
2025-05-25 13:20:51 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - BT] Opções FILTRADAS por Um=3.6 e Norma=IEC.
2025-05-25 13:20:51 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT-NEUTRO] Valores do MCP: NBI=None, SIL=None
2025-05-25 13:20:51 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT-NEUTRO] Valores salvos: NBI=None, SIL=None
2025-05-25 13:20:51 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO-NEUTRO] Valores do MCP: NBI=None, SIL=None
2025-05-25 13:20:51 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - AT] Opções FILTRADAS por Um=69.0 e Norma=IEC.
2025-05-25 13:20:51 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - TERCIARIO] Opções FILTRADAS por Um=24.0 e Norma=IEC.
2025-05-25 13:20:51 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT-NEUTRO] Valores salvos: NBI=None, SIL=None
2025-05-25 13:20:51 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO-NEUTRO] Valores salvos: NBI=None, SIL=None
2025-05-25 13:20:51 - callbacks.global_updates - WARNING - Dados essenciais ausentes no transformer_store_data
2025-05-25 13:20:51 - components.transformer_info_template - WARNING - transformer_data inválido ou vazio: {}
2025-05-25 13:20:51 - callbacks.global_updates - DEBUG - Painel atualizado em 2.0ms
2025-05-25 13:20:51 - callbacks.losses - CRITICAL - [LOSSES POPULATE CARGA] Acionado. Aba: tab-carga, Trigger: N/A
2025-05-25 13:20:53 - layouts.impulse - INFO - Criando layout de impulso...
2025-05-25 13:20:53 - layouts.impulse - INFO - [Impulse] Dados do transformador obtidos do MCP: 58
2025-05-25 13:20:53 - callbacks.global_updates - WARNING - Dados essenciais ausentes no transformer_store_data
2025-05-25 13:20:53 - components.transformer_info_template - WARNING - transformer_data inválido ou vazio: {}
2025-05-25 13:20:53 - callbacks.global_updates - DEBUG - Painel atualizado em 0.0ms
2025-05-25 13:20:57 - layouts.dieletric_analysis - INFO - Creating Dielectric Analysis layout (from dieletric_analysis.py)...
2025-05-25 13:20:57 - app_core.standards - ERROR - Arquivo JSON 'c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\tabela.json' não encontrado.
2025-05-25 13:20:57 - app_core.standards - ERROR - Arquivo JSON 'c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\tabela.json' não encontrado.
2025-05-25 13:20:57 - app_core.standards - ERROR - Arquivo JSON 'c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\tabela.json' não encontrado.
2025-05-25 13:20:57 - app_core.standards - ERROR - Arquivo JSON 'c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\tabela.json' não encontrado.
2025-05-25 13:20:57 - app_core.standards - CRITICAL - Falha CRÍTICA ao inicializar as classes das normas: Falha ao carregar dados JSON para NBR ou IEEE.
Traceback (most recent call last):
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\app_core\standards.py", line 613, in __init__
    raise RuntimeError("Falha ao carregar dados JSON para NBR ou IEEE.")
RuntimeError: Falha ao carregar dados JSON para NBR ou IEEE.
2025-05-25 13:20:57 - layouts.dieletric_analysis - WARNING - VerificadorTransformador is invalid upon creation in create_dielectric_layout.
2025-05-25 13:20:57 - components.ui_elements - WARNING - Verificador inválido ao criar coluna dielétrica (Alta Tensão).
2025-05-25 13:20:57 - components.ui_elements - WARNING - Verificador inválido ao criar coluna dielétrica (Baixa Tensão).
2025-05-25 13:20:57 - components.ui_elements - WARNING - Verificador inválido ao criar coluna dielétrica (Terciário).
2025-05-25 13:20:57 - callbacks.global_updates - WARNING - Dados essenciais ausentes no transformer_store_data
2025-05-25 13:20:57 - components.transformer_info_template - WARNING - transformer_data inválido ou vazio: {}
2025-05-25 13:20:57 - app_core.standards - ERROR - Arquivo JSON 'c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\tabela.json' não encontrado.
2025-05-25 13:20:57 - callbacks.global_updates - DEBUG - Painel atualizado em 0.0ms
2025-05-25 13:20:57 - app_core.standards - ERROR - Arquivo JSON 'c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\tabela.json' não encontrado.
2025-05-25 13:20:57 - app_core.standards - ERROR - Arquivo JSON 'c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\tabela.json' não encontrado.
2025-05-25 13:20:57 - app_core.standards - ERROR - Arquivo JSON 'c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\tabela.json' não encontrado.
2025-05-25 13:20:57 - app_core.standards - CRITICAL - Falha CRÍTICA ao inicializar as classes das normas: Falha ao carregar dados JSON para NBR ou IEEE.
Traceback (most recent call last):
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\app_core\standards.py", line 613, in __init__
    raise RuntimeError("Falha ao carregar dados JSON para NBR ou IEEE.")
RuntimeError: Falha ao carregar dados JSON para NBR ou IEEE.
2025-05-25 13:20:57 - callbacks.dieletric_analysis - ERROR - Instância VerificadorTransformador criada, mas inválida (falha dados?).
2025-05-25 13:20:58 - layouts.applied_voltage - INFO - [Applied Voltage Layout] Creating layout...
2025-05-25 13:20:58 - callbacks.global_updates - WARNING - Dados essenciais ausentes no transformer_store_data
2025-05-25 13:20:58 - app - ERROR - Exception on /_dash-update-component [POST]
Traceback (most recent call last):
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\flask\app.py", line 2190, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\flask\app.py", line 1486, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\dash\dash.py", line 1373, in dispatch
    ctx.run(
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\dash\_callback.py", line 465, in add_context
    output_value = _invoke_callback(func, *func_args, **func_kwargs)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\dash\_callback.py", line 40, in _invoke_callback
    return func(*args, **kwargs)  # %% callback invoked %%
           ^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\callbacks\applied_voltage.py", line 821, in load_applied_voltage_inputs
    raise dash.no_update
TypeError: exceptions must derive from BaseException
2025-05-25 13:20:58 - components.transformer_info_template - WARNING - transformer_data inválido ou vazio: {}
2025-05-25 13:20:58 - callbacks.global_updates - DEBUG - Painel atualizado em 3.33ms
2025-05-25 13:20:59 - app - ERROR - Exception on /_dash-update-component [POST]
Traceback (most recent call last):
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\flask\app.py", line 2190, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\flask\app.py", line 1486, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\dash\dash.py", line 1373, in dispatch
    ctx.run(
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\dash\_callback.py", line 465, in add_context
    output_value = _invoke_callback(func, *func_args, **func_kwargs)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\dash\_callback.py", line 40, in _invoke_callback
    return func(*args, **kwargs)  # %% callback invoked %%
           ^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\callbacks\applied_voltage.py", line 784, in load_applied_voltage_inputs
    raise dash.no_update
TypeError: exceptions must derive from BaseException
2025-05-25 13:20:59 - layouts.induced_voltage - INFO - [Induced Voltage] Usando dados vazios inicialmente, serão preenchidos via callback
2025-05-25 13:20:59 - components.ui_elements - WARNING - Style fornecido tanto via input_style quanto via kwargs para frequencia-teste. Usando input_style.
2025-05-25 13:20:59 - components.ui_elements - WARNING - Style fornecido tanto via input_style quanto via kwargs para capacitancia. Usando input_style.
2025-05-25 13:20:59 - callbacks.global_updates - WARNING - Dados essenciais ausentes no transformer_store_data
2025-05-25 13:20:59 - components.transformer_info_template - WARNING - transformer_data inválido ou vazio: {}
2025-05-25 13:20:59 - callbacks.global_updates - DEBUG - Painel atualizado em 1.0ms
2025-05-25 13:21:01 - layouts.short_circuit - INFO - [Short Circuit] Dados do transformador obtidos do MCP: 58
2025-05-25 13:21:02 - callbacks.global_updates - WARNING - Dados essenciais ausentes no transformer_store_data
2025-05-25 13:21:02 - components.transformer_info_template - WARNING - transformer_data inválido ou vazio: {}
2025-05-25 13:21:02 - callbacks.global_updates - DEBUG - Painel atualizado em 23.86ms
2025-05-25 13:21:03 - layouts.temperature_rise - INFO - [Temperature Rise] Dados do transformador obtidos do MCP: 58
2025-05-25 13:21:03 - callbacks.global_updates - WARNING - Dados essenciais ausentes no transformer_store_data
2025-05-25 13:21:03 - components.transformer_info_template - WARNING - transformer_data inválido ou vazio: {}
2025-05-25 13:21:03 - callbacks.global_updates - DEBUG - Painel atualizado em 1.11ms
2025-05-25 13:21:04 - layouts.history - INFO - Criando layout de Histórico de Sessões (VERSÃO REFEITA)
2025-05-25 13:21:04 - callbacks.global_updates - WARNING - Dados essenciais ausentes no transformer_store_data
2025-05-25 13:21:04 - components.transformer_info_template - WARNING - transformer_data inválido ou vazio: {}
2025-05-25 13:21:04 - callbacks.global_updates - DEBUG - Painel atualizado em 0.3ms
2025-05-25 13:21:05 - layouts.standards_consultation - INFO - Criando layout de consulta de normas técnicas
2025-05-25 13:21:05 - callbacks.global_updates - WARNING - Dados essenciais ausentes no transformer_store_data
2025-05-25 13:21:05 - components.transformer_info_template - WARNING - transformer_data inválido ou vazio: {}
2025-05-25 13:21:05 - callbacks.global_updates - DEBUG - Painel atualizado em 0.0ms
2025-05-25 13:21:09 - layouts.transformer_inputs - INFO - Criando layout Dados Básicos (v5 - Layout Geral e Pesos em 2 linhas)...
2025-05-25 13:21:09 - layouts.transformer_inputs - INFO - Classes de tensão carregadas para dropdown: 27 opções.
2025-05-25 13:21:09 - callbacks.global_updates - WARNING - Dados essenciais ausentes no transformer_store_data
2025-05-25 13:21:09 - components.transformer_info_template - WARNING - transformer_data inválido ou vazio: {}
2025-05-25 13:21:09 - callbacks.global_updates - DEBUG - Painel atualizado em 0.99ms
2025-05-25 13:21:09 - callbacks.transformer_inputs - INFO - [LOAD TransformerInputs] Acionado por: None
2025-05-25 13:21:09 - callbacks.transformer_inputs - INFO - [LOAD TransformerInputs] Opções dos dropdowns ainda não foram populadas, aguardando...
2025-05-25 13:21:09 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_kv: 69, Tensão: 69, Path: /dados
2025-05-25 13:21:09 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT] Valores do MCP: NBI=None, SIL=None, TA=None, TI=None
2025-05-25 13:21:09 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT-NEUTRO] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_Neutro: None, Conexão: triangulo, Tensão: 69
2025-05-25 13:21:09 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_kv: 3.6, Tensão: 13.8, Path: /dados
2025-05-25 13:21:09 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT] Valores salvos: NBI=None, SIL=None, TA=None, TI=
2025-05-25 13:21:09 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT-NEUTRO] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_Neutro: None, Conexão: triangulo, Tensão: 13.8
2025-05-25 13:21:09 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT-NEUTRO] Valores do MCP: NBI=None, SIL=None
2025-05-25 13:21:09 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT] Valores do MCP: NBI=None, SIL=None, TA=None, TI=None
2025-05-25 13:21:09 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_kv: 24, Tensão: 13, Path: /dados
2025-05-25 13:21:09 - app_core.isolation_repo - WARNING - [ISOLATION] Nenhum registro encontrado para Um=69.0kV e norma=IEC/NBR. Usando valores padrão.
2025-05-25 13:21:09 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT-NEUTRO] Valores do MCP: NBI=None, SIL=None
2025-05-25 13:21:09 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO-NEUTRO] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_Neutro: None, Conexão: triangulo, Tensão: 13
2025-05-25 13:21:09 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT-NEUTRO] Valores salvos: NBI=None, SIL=None
2025-05-25 13:21:09 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT] Valores salvos: NBI=None, SIL=None, TA=None, TI=/dados
2025-05-25 13:21:09 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO] Valores do MCP: NBI=None, SIL=None, TA=None, TI=None
2025-05-25 13:21:09 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - AT] Opções FILTRADAS por Um=69.0 e Norma=IEC.
2025-05-25 13:21:09 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT-NEUTRO] Valores salvos: NBI=None, SIL=None
2025-05-25 13:21:09 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO-NEUTRO] Valores do MCP: NBI=None, SIL=None
2025-05-25 13:21:09 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO] Valores salvos: NBI=None, SIL=None, TA=None, TI=/dados
2025-05-25 13:21:09 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - BT] Opções FILTRADAS por Um=3.6 e Norma=IEC.
2025-05-25 13:21:09 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO-NEUTRO] Valores salvos: NBI=None, SIL=None
2025-05-25 13:21:09 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - TERCIARIO] Opções FILTRADAS por Um=24.0 e Norma=IEC.
2025-05-25 13:21:10 - callbacks.transformer_inputs - INFO - [LOAD TransformerInputs] Acionado por: url
2025-05-25 13:21:10 - callbacks.transformer_inputs - DEBUG - [LOAD TransformerInputs] Não estamos na página de dados (pathname=/perdas, clean_path=perdas), prevenindo atualização
2025-05-25 13:21:10 - utils.mcp_persistence - INFO - [ensure_mcp_data_propagation] Store de origem transformer-inputs-store É a fonte-de-verdade para campos básicos
2025-05-25 13:21:10 - app_core.transformer_mcp - DEBUG - [MCP SET] Dados definidos para store 'losses-store'.
2025-05-25 13:21:10 - utils.mcp_persistence - INFO - [ensure_mcp_data_propagation] Dados básicos propagados para losses-store
2025-05-25 13:21:10 - layouts.losses - INFO - Creating Losses layout...
2025-05-25 13:21:10 - layouts.losses - INFO - [Losses Layout] Dados do transformador obtidos do losses-store.
2025-05-25 13:21:10 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT] Acionado por: url. Norma: IEC, Um_kv: 69, Tensão: 69, Path: /perdas
2025-05-25 13:21:10 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT] Valores do MCP: NBI=None, SIL=None, TA=None, TI=None
2025-05-25 13:21:10 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT] Acionado por: url. Norma: IEC, Um_kv: 3.6, Tensão: 13.8, Path: /perdas
2025-05-25 13:21:10 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT] Valores salvos: NBI=None, SIL=None, TA=None, TI=
2025-05-25 13:21:10 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO] Acionado por: url. Norma: IEC, Um_kv: 24, Tensão: 13, Path: /perdas
2025-05-25 13:21:10 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT] Valores do MCP: NBI=None, SIL=None, TA=None, TI=None
2025-05-25 13:21:10 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT-NEUTRO] Acionado por: url. Norma: IEC, Um_Neutro: None, Conexão: triangulo, Tensão: 69
2025-05-25 13:21:10 - app_core.isolation_repo - WARNING - [ISOLATION] Nenhum registro encontrado para Um=69.0kV e norma=IEC/NBR. Usando valores padrão.
2025-05-25 13:21:10 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT-NEUTRO] Acionado por: url. Norma: IEC, Um_Neutro: None, Conexão: triangulo, Tensão: 13.8
2025-05-25 13:21:10 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO] Valores do MCP: NBI=None, SIL=None, TA=None, TI=None
2025-05-25 13:21:10 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT] Valores salvos: NBI=None, SIL=None, TA=None, TI=/perdas
2025-05-25 13:21:10 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO-NEUTRO] Acionado por: url. Norma: IEC, Um_Neutro: None, Conexão: triangulo, Tensão: 13
2025-05-25 13:21:10 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT-NEUTRO] Valores do MCP: NBI=None, SIL=None
2025-05-25 13:21:10 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - AT] Opções FILTRADAS por Um=69.0 e Norma=IEC.
2025-05-25 13:21:10 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT-NEUTRO] Valores do MCP: NBI=None, SIL=None
2025-05-25 13:21:10 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO] Valores salvos: NBI=None, SIL=None, TA=None, TI=/perdas
2025-05-25 13:21:10 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO-NEUTRO] Valores do MCP: NBI=None, SIL=None
2025-05-25 13:21:10 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT-NEUTRO] Valores salvos: NBI=None, SIL=None
2025-05-25 13:21:10 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - BT] Opções FILTRADAS por Um=3.6 e Norma=IEC.
2025-05-25 13:21:10 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT-NEUTRO] Valores salvos: NBI=None, SIL=None
2025-05-25 13:21:10 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO-NEUTRO] Valores salvos: NBI=None, SIL=None
2025-05-25 13:21:10 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - TERCIARIO] Opções FILTRADAS por Um=24.0 e Norma=IEC.
2025-05-25 13:21:10 - callbacks.global_updates - WARNING - Dados essenciais ausentes no transformer_store_data
2025-05-25 13:21:10 - components.transformer_info_template - WARNING - transformer_data inválido ou vazio: {}
2025-05-25 13:21:10 - callbacks.global_updates - DEBUG - Painel atualizado em 2.0ms
2025-05-25 13:21:10 - callbacks.losses - CRITICAL - [LOSSES POPULATE CARGA] Acionado. Aba: tab-carga, Trigger: N/A
2025-05-25 13:21:11 - layouts.impulse - INFO - Criando layout de impulso...
2025-05-25 13:21:11 - layouts.impulse - INFO - [Impulse] Dados do transformador obtidos do MCP: 58
2025-05-25 13:21:11 - callbacks.global_updates - WARNING - Dados essenciais ausentes no transformer_store_data
2025-05-25 13:21:11 - components.transformer_info_template - WARNING - transformer_data inválido ou vazio: {}
2025-05-25 13:21:11 - callbacks.global_updates - DEBUG - Painel atualizado em 2.33ms
2025-05-25 13:21:12 - layouts.dieletric_analysis - INFO - Creating Dielectric Analysis layout (from dieletric_analysis.py)...
2025-05-25 13:21:12 - app_core.standards - ERROR - Arquivo JSON 'c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\tabela.json' não encontrado.
2025-05-25 13:21:12 - app_core.standards - ERROR - Arquivo JSON 'c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\tabela.json' não encontrado.
2025-05-25 13:21:12 - app_core.standards - ERROR - Arquivo JSON 'c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\tabela.json' não encontrado.
2025-05-25 13:21:12 - app_core.standards - ERROR - Arquivo JSON 'c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\tabela.json' não encontrado.
2025-05-25 13:21:12 - app_core.standards - CRITICAL - Falha CRÍTICA ao inicializar as classes das normas: Falha ao carregar dados JSON para NBR ou IEEE.
Traceback (most recent call last):
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\app_core\standards.py", line 613, in __init__
    raise RuntimeError("Falha ao carregar dados JSON para NBR ou IEEE.")
RuntimeError: Falha ao carregar dados JSON para NBR ou IEEE.
2025-05-25 13:21:12 - layouts.dieletric_analysis - WARNING - VerificadorTransformador is invalid upon creation in create_dielectric_layout.
2025-05-25 13:21:12 - components.ui_elements - WARNING - Verificador inválido ao criar coluna dielétrica (Alta Tensão).
2025-05-25 13:21:12 - components.ui_elements - WARNING - Verificador inválido ao criar coluna dielétrica (Baixa Tensão).
2025-05-25 13:21:12 - components.ui_elements - WARNING - Verificador inválido ao criar coluna dielétrica (Terciário).
2025-05-25 13:21:12 - callbacks.global_updates - WARNING - Dados essenciais ausentes no transformer_store_data
2025-05-25 13:21:12 - components.transformer_info_template - WARNING - transformer_data inválido ou vazio: {}
2025-05-25 13:21:12 - app_core.standards - ERROR - Arquivo JSON 'c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\tabela.json' não encontrado.
2025-05-25 13:21:12 - callbacks.global_updates - DEBUG - Painel atualizado em 1.0ms
2025-05-25 13:21:12 - app_core.standards - ERROR - Arquivo JSON 'c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\tabela.json' não encontrado.
2025-05-25 13:21:12 - app_core.standards - ERROR - Arquivo JSON 'c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\tabela.json' não encontrado.
2025-05-25 13:21:12 - app_core.standards - ERROR - Arquivo JSON 'c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\tabela.json' não encontrado.
2025-05-25 13:21:12 - app_core.standards - CRITICAL - Falha CRÍTICA ao inicializar as classes das normas: Falha ao carregar dados JSON para NBR ou IEEE.
Traceback (most recent call last):
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\app_core\standards.py", line 613, in __init__
    raise RuntimeError("Falha ao carregar dados JSON para NBR ou IEEE.")
RuntimeError: Falha ao carregar dados JSON para NBR ou IEEE.
2025-05-25 13:21:12 - callbacks.dieletric_analysis - ERROR - Instância VerificadorTransformador criada, mas inválida (falha dados?).
2025-05-25 13:21:13 - layouts.applied_voltage - INFO - [Applied Voltage Layout] Creating layout...
2025-05-25 13:21:13 - callbacks.global_updates - WARNING - Dados essenciais ausentes no transformer_store_data
2025-05-25 13:21:13 - app - ERROR - Exception on /_dash-update-component [POST]
Traceback (most recent call last):
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\flask\app.py", line 2190, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\flask\app.py", line 1486, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\dash\dash.py", line 1373, in dispatch
    ctx.run(
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\dash\_callback.py", line 465, in add_context
    output_value = _invoke_callback(func, *func_args, **func_kwargs)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\dash\_callback.py", line 40, in _invoke_callback
    return func(*args, **kwargs)  # %% callback invoked %%
           ^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\callbacks\applied_voltage.py", line 821, in load_applied_voltage_inputs
    raise dash.no_update
TypeError: exceptions must derive from BaseException
2025-05-25 13:21:13 - components.transformer_info_template - WARNING - transformer_data inválido ou vazio: {}
2025-05-25 13:21:13 - callbacks.global_updates - DEBUG - Painel atualizado em 4.0ms
2025-05-25 13:21:13 - app - ERROR - Exception on /_dash-update-component [POST]
Traceback (most recent call last):
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\flask\app.py", line 2190, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\flask\app.py", line 1486, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\dash\dash.py", line 1373, in dispatch
    ctx.run(
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\dash\_callback.py", line 465, in add_context
    output_value = _invoke_callback(func, *func_args, **func_kwargs)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\dash\_callback.py", line 40, in _invoke_callback
    return func(*args, **kwargs)  # %% callback invoked %%
           ^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\callbacks\applied_voltage.py", line 784, in load_applied_voltage_inputs
    raise dash.no_update
TypeError: exceptions must derive from BaseException
2025-05-25 13:21:13 - layouts.induced_voltage - INFO - [Induced Voltage] Usando dados vazios inicialmente, serão preenchidos via callback
2025-05-25 13:21:13 - components.ui_elements - WARNING - Style fornecido tanto via input_style quanto via kwargs para frequencia-teste. Usando input_style.
2025-05-25 13:21:13 - components.ui_elements - WARNING - Style fornecido tanto via input_style quanto via kwargs para capacitancia. Usando input_style.
2025-05-25 13:21:13 - callbacks.global_updates - WARNING - Dados essenciais ausentes no transformer_store_data
2025-05-25 13:21:13 - components.transformer_info_template - WARNING - transformer_data inválido ou vazio: {}
2025-05-25 13:21:13 - callbacks.global_updates - DEBUG - Painel atualizado em 1.0ms
2025-05-25 13:21:14 - layouts.short_circuit - INFO - [Short Circuit] Dados do transformador obtidos do MCP: 58
2025-05-25 13:21:14 - callbacks.global_updates - WARNING - Dados essenciais ausentes no transformer_store_data
2025-05-25 13:21:14 - components.transformer_info_template - WARNING - transformer_data inválido ou vazio: {}
2025-05-25 13:21:14 - callbacks.global_updates - DEBUG - Painel atualizado em 9.53ms
2025-05-25 13:21:14 - layouts.temperature_rise - INFO - [Temperature Rise] Dados do transformador obtidos do MCP: 58
2025-05-25 13:21:14 - callbacks.global_updates - WARNING - Dados essenciais ausentes no transformer_store_data
2025-05-25 13:21:14 - components.transformer_info_template - WARNING - transformer_data inválido ou vazio: {}
2025-05-25 13:21:14 - callbacks.global_updates - DEBUG - Painel atualizado em 0.0ms
2025-05-25 13:21:15 - layouts.history - INFO - Criando layout de Histórico de Sessões (VERSÃO REFEITA)
2025-05-25 13:21:15 - callbacks.global_updates - WARNING - Dados essenciais ausentes no transformer_store_data
2025-05-25 13:21:15 - components.transformer_info_template - WARNING - transformer_data inválido ou vazio: {}
2025-05-25 13:21:15 - callbacks.global_updates - DEBUG - Painel atualizado em 0.0ms
2025-05-25 13:21:16 - layouts.standards_consultation - INFO - Criando layout de consulta de normas técnicas
2025-05-25 13:21:16 - callbacks.global_updates - WARNING - Dados essenciais ausentes no transformer_store_data
2025-05-25 13:21:16 - components.transformer_info_template - WARNING - transformer_data inválido ou vazio: {}
2025-05-25 13:21:16 - callbacks.global_updates - DEBUG - Painel atualizado em 0.0ms
2025-05-25 13:21:17 - layouts.transformer_inputs - INFO - Criando layout Dados Básicos (v5 - Layout Geral e Pesos em 2 linhas)...
2025-05-25 13:21:17 - layouts.transformer_inputs - INFO - Classes de tensão carregadas para dropdown: 27 opções.
2025-05-25 13:21:17 - callbacks.global_updates - WARNING - Dados essenciais ausentes no transformer_store_data
2025-05-25 13:21:17 - components.transformer_info_template - WARNING - transformer_data inválido ou vazio: {}
2025-05-25 13:21:17 - callbacks.global_updates - DEBUG - Painel atualizado em 1.0ms
2025-05-25 13:21:17 - callbacks.transformer_inputs - INFO - [LOAD TransformerInputs] Acionado por: None
2025-05-25 13:21:17 - callbacks.transformer_inputs - INFO - [LOAD TransformerInputs] Opções dos dropdowns ainda não foram populadas, aguardando...
2025-05-25 13:21:17 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_kv: 69, Tensão: 69, Path: /dados
2025-05-25 13:21:17 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT-NEUTRO] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_Neutro: None, Conexão: triangulo, Tensão: 69
2025-05-25 13:21:17 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT] Valores do MCP: NBI=None, SIL=None, TA=None, TI=None
2025-05-25 13:21:17 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_kv: 3.6, Tensão: 13.8, Path: /dados
2025-05-25 13:21:17 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT-NEUTRO] Valores do MCP: NBI=None, SIL=None
2025-05-25 13:21:17 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT-NEUTRO] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_Neutro: None, Conexão: triangulo, Tensão: 13.8
2025-05-25 13:21:17 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT] Valores salvos: NBI=None, SIL=None, TA=None, TI=
2025-05-25 13:21:17 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT] Valores do MCP: NBI=None, SIL=None, TA=None, TI=None
2025-05-25 13:21:17 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT-NEUTRO] Valores salvos: NBI=None, SIL=None
2025-05-25 13:21:17 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_kv: 24, Tensão: 13, Path: /dados
2025-05-25 13:21:17 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT-NEUTRO] Valores do MCP: NBI=None, SIL=None
2025-05-25 13:21:17 - app_core.isolation_repo - WARNING - [ISOLATION] Nenhum registro encontrado para Um=69.0kV e norma=IEC/NBR. Usando valores padrão.
2025-05-25 13:21:17 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO-NEUTRO] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_Neutro: None, Conexão: triangulo, Tensão: 13
2025-05-25 13:21:17 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT] Valores salvos: NBI=None, SIL=None, TA=None, TI=/dados
2025-05-25 13:21:17 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO] Valores do MCP: NBI=None, SIL=None, TA=None, TI=None
2025-05-25 13:21:17 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT-NEUTRO] Valores salvos: NBI=None, SIL=None
2025-05-25 13:21:17 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - AT] Opções FILTRADAS por Um=69.0 e Norma=IEC.
2025-05-25 13:21:17 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO-NEUTRO] Valores do MCP: NBI=None, SIL=None
2025-05-25 13:21:17 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO] Valores salvos: NBI=None, SIL=None, TA=None, TI=/dados
2025-05-25 13:21:17 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - BT] Opções FILTRADAS por Um=3.6 e Norma=IEC.
2025-05-25 13:21:17 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO-NEUTRO] Valores salvos: NBI=None, SIL=None
2025-05-25 13:21:17 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - TERCIARIO] Opções FILTRADAS por Um=24.0 e Norma=IEC.
2025-05-25 13:21:28 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT] Acionado por: tensao_at. Norma: IEC, Um_kv: 69, Tensão: 69.1, Path: /dados
2025-05-25 13:21:28 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT-NEUTRO] Acionado por: tensao_at. Norma: IEC, Um_Neutro: None, Conexão: triangulo, Tensão: 69.1
2025-05-25 13:21:28 - callbacks.transformer_inputs - DEBUG - [UpdateTransformerCalc] Acionado por: tensao_at
2025-05-25 13:21:28 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT] Valores do MCP: NBI=None, SIL=None, TA=None, TI=None
2025-05-25 13:21:28 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT-NEUTRO] Valores do MCP: NBI=None, SIL=None
2025-05-25 13:21:28 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT] Valores salvos: NBI=None, SIL=None, TA=None, TI=
2025-05-25 13:21:28 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT-NEUTRO] Valores salvos: NBI=None, SIL=None
2025-05-25 13:21:28 - callbacks.transformer_inputs - INFO - [UpdateTransformerCalc] Sincronizando elevação de enrolamento: AT=BT=Terciário=55°C
2025-05-25 13:21:28 - app_core.isolation_repo - WARNING - [ISOLATION] Nenhum registro encontrado para Um=69.0kV e norma=IEC/NBR. Usando valores padrão.
2025-05-25 13:21:28 - app_core.transformer_mcp - DEBUG - [MCP SET] Dados definidos para store 'transformer-inputs-store'.
2025-05-25 13:21:28 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - AT] Opções FILTRADAS por Um=69.0 e Norma=IEC.
2025-05-25 13:21:28 - app_core.transformer_mcp - INFO - [MCP SAVE_TO_DISK] Salvando estado do MCP em disco...
2025-05-25 13:21:28 - app_core.transformer_mcp - INFO - [MCP SAVE_TO_DISK] Estado do MCP salvo em disco com sucesso.
2025-05-25 13:21:28 - callbacks.transformer_inputs - DEBUG - [UpdateTransformerCalc] MCP atualizado e salvo no disco.
2025-05-25 13:21:28 - callbacks.transformer_inputs - DEBUG - [UpdateTransformerCalc] Dados propagados para todos os stores alvo.
2025-05-25 13:21:28 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT] Acionado por: tensao_at. Norma: IEC, Um_kv: 69, Tensão: 69, Path: /dados
2025-05-25 13:21:28 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT] Valores do MCP: NBI=None, SIL=None, TA=None, TI=None
2025-05-25 13:21:28 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT-NEUTRO] Acionado por: tensao_at. Norma: IEC, Um_Neutro: None, Conexão: triangulo, Tensão: 69
2025-05-25 13:21:28 - callbacks.transformer_inputs - DEBUG - [UpdateTransformerCalc] Acionado por: tensao_at
2025-05-25 13:21:28 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT] Valores salvos: NBI=None, SIL=None, TA=None, TI=
2025-05-25 13:21:28 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT-NEUTRO] Valores do MCP: NBI=None, SIL=None
2025-05-25 13:21:28 - callbacks.transformer_inputs - INFO - [UpdateTransformerCalc] Sincronizando elevação de enrolamento: AT=BT=Terciário=55°C
2025-05-25 13:21:28 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT-NEUTRO] Valores salvos: NBI=None, SIL=None
2025-05-25 13:21:28 - app_core.transformer_mcp - DEBUG - [MCP SET] Dados definidos para store 'transformer-inputs-store'.
2025-05-25 13:21:28 - app_core.isolation_repo - WARNING - [ISOLATION] Nenhum registro encontrado para Um=69.0kV e norma=IEC/NBR. Usando valores padrão.
2025-05-25 13:21:28 - app_core.transformer_mcp - INFO - [MCP SAVE_TO_DISK] Salvando estado do MCP em disco...
2025-05-25 13:21:28 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - AT] Opções FILTRADAS por Um=69.0 e Norma=IEC.
2025-05-25 13:21:28 - app_core.transformer_mcp - INFO - [MCP SAVE_TO_DISK] Estado do MCP salvo em disco com sucesso.
2025-05-25 13:21:28 - callbacks.transformer_inputs - DEBUG - [UpdateTransformerCalc] MCP atualizado e salvo no disco.
2025-05-25 13:21:28 - callbacks.transformer_inputs - DEBUG - [UpdateTransformerCalc] Dados propagados para todos os stores alvo.
2025-05-25 13:22:34 - callbacks.temperature_rise - INFO - Registrando callbacks do módulo temperature_rise para app Simulador de Testes de Transformadores (IEC/IEEE/ABNT) EPS 1500...
2025-05-25 13:22:34 - callbacks.temperature_rise - INFO - Callbacks do módulo temperature_rise já registrados via decoradores @app.callback.
2025-05-25 13:22:34 - app - INFO - Callbacks registrados. Total: 50
2025-05-25 13:22:34 - callbacks.insulation_level_callbacks - INFO - Registrando callbacks de níveis de isolamento (APENAS OPÇÕES - V2)...
2025-05-25 13:22:34 - callbacks.insulation_level_callbacks - INFO - Callbacks de níveis de isolamento (APENAS OPÇÕES - V2) registrados.
2025-05-25 13:22:34 - callbacks.transformer_inputs - INFO - Registrando callbacks do módulo transformer_inputs para app Simulador de Testes de Transformadores (IEC/IEEE/ABNT) EPS 1500...
2025-05-25 13:22:34 - callbacks.temperature_rise - INFO - Registrando callbacks do módulo temperature_rise para app Simulador de Testes de Transformadores (IEC/IEEE/ABNT) EPS 1500...
2025-05-25 13:22:34 - callbacks.temperature_rise - INFO - Callbacks do módulo temperature_rise já registrados via decoradores @app.callback.
2025-05-25 13:22:34 - __main__ - INFO - Callbacks registrados. Total: 35
2025-05-25 13:22:34 - __main__ - INFO - Iniciando servidor Dash em http://127.0.0.1:8050/ (Debug: True)
2025-05-25 13:22:34 - dash.dash - INFO - Dash is running on http://127.0.0.1:8050/

2025-05-25 13:22:36 - callbacks.global_updates - WARNING - Dados essenciais ausentes no transformer_store_data
2025-05-25 13:22:36 - components.transformer_info_template - WARNING - transformer_data inválido ou vazio: {}
2025-05-25 13:22:36 - layouts.transformer_inputs - INFO - Criando layout Dados Básicos (v5 - Layout Geral e Pesos em 2 linhas)...
2025-05-25 13:22:36 - callbacks.global_updates - DEBUG - Painel atualizado em 1.0ms
2025-05-25 13:22:36 - layouts.transformer_inputs - INFO - Classes de tensão carregadas para dropdown: 27 opções.
2025-05-25 13:22:36 - callbacks.global_updates - WARNING - Dados essenciais ausentes no transformer_store_data
2025-05-25 13:22:36 - components.transformer_info_template - WARNING - transformer_data inválido ou vazio: {}
2025-05-25 13:22:36 - callbacks.global_updates - DEBUG - Painel atualizado em 1.0ms
2025-05-25 13:22:36 - callbacks.transformer_inputs - INFO - [LOAD TransformerInputs] Acionado por: None
2025-05-25 13:22:36 - callbacks.transformer_inputs - INFO - [LOAD TransformerInputs] Opções dos dropdowns ainda não foram populadas, aguardando...
2025-05-25 13:22:36 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_kv: 69, Tensão: 69, Path: /dados
2025-05-25 13:22:36 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT-NEUTRO] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_Neutro: None, Conexão: triangulo, Tensão: 69
2025-05-25 13:22:36 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT] Valores do MCP: NBI=None, SIL=None, TA=None, TI=None
2025-05-25 13:22:36 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_kv: 3.6, Tensão: 13.8, Path: /dados
2025-05-25 13:22:36 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT-NEUTRO] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_Neutro: None, Conexão: triangulo, Tensão: 13.8
2025-05-25 13:22:36 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT-NEUTRO] Valores do MCP: NBI=None, SIL=None
2025-05-25 13:22:36 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT] Valores salvos: NBI=None, SIL=None, TA=None, TI=
2025-05-25 13:22:36 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT] Valores do MCP: NBI=None, SIL=None, TA=None, TI=None
2025-05-25 13:22:36 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT-NEUTRO] Valores do MCP: NBI=None, SIL=None
2025-05-25 13:22:36 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_kv: 24, Tensão: 13, Path: /dados
2025-05-25 13:22:36 - app_core.isolation_repo - WARNING - [ISOLATION] Nenhum registro encontrado para Um=69.0kV e norma=IEC/NBR. Usando valores padrão.
2025-05-25 13:22:36 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT-NEUTRO] Valores salvos: NBI=None, SIL=None
2025-05-25 13:22:36 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO-NEUTRO] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_Neutro: None, Conexão: triangulo, Tensão: 13
2025-05-25 13:22:36 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT] Valores salvos: NBI=None, SIL=None, TA=None, TI=/dados
2025-05-25 13:22:36 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT-NEUTRO] Valores salvos: NBI=None, SIL=None
2025-05-25 13:22:36 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO] Valores do MCP: NBI=None, SIL=None, TA=None, TI=None
2025-05-25 13:22:36 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - AT] Opções FILTRADAS por Um=69.0 e Norma=IEC.
2025-05-25 13:22:36 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO-NEUTRO] Valores do MCP: NBI=None, SIL=None
2025-05-25 13:22:36 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO] Valores salvos: NBI=None, SIL=None, TA=None, TI=/dados
2025-05-25 13:22:36 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - BT] Opções FILTRADAS por Um=3.6 e Norma=IEC.
2025-05-25 13:22:36 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO-NEUTRO] Valores salvos: NBI=None, SIL=None
2025-05-25 13:22:36 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - TERCIARIO] Opções FILTRADAS por Um=24.0 e Norma=IEC.
2025-05-25 13:23:03 - callbacks.transformer_inputs - INFO - [LOAD TransformerInputs] Acionado por: url
2025-05-25 13:23:03 - callbacks.transformer_inputs - DEBUG - [LOAD TransformerInputs] Não estamos na página de dados (pathname=/perdas, clean_path=perdas), prevenindo atualização
2025-05-25 13:23:03 - utils.mcp_persistence - INFO - [ensure_mcp_data_propagation] Store de origem transformer-inputs-store É a fonte-de-verdade para campos básicos
2025-05-25 13:23:03 - app_core.transformer_mcp - DEBUG - [MCP SET] Dados definidos para store 'losses-store'.
2025-05-25 13:23:03 - utils.mcp_persistence - INFO - [ensure_mcp_data_propagation] Dados básicos propagados para losses-store
2025-05-25 13:23:03 - layouts.losses - INFO - Creating Losses layout...
2025-05-25 13:23:03 - layouts.losses - INFO - [Losses Layout] Dados do transformador obtidos do losses-store.
2025-05-25 13:23:03 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT] Acionado por: url. Norma: IEC, Um_kv: 69, Tensão: 69, Path: /perdas
2025-05-25 13:23:03 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT] Acionado por: url. Norma: IEC, Um_kv: 3.6, Tensão: 13.8, Path: /perdas
2025-05-25 13:23:03 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT] Valores do MCP: NBI=None, SIL=None, TA=None, TI=None
2025-05-25 13:23:03 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT] Valores do MCP: NBI=None, SIL=None, TA=None, TI=None
2025-05-25 13:23:03 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO] Acionado por: url. Norma: IEC, Um_kv: 24, Tensão: 13, Path: /perdas
2025-05-25 13:23:03 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT] Valores salvos: NBI=None, SIL=None, TA=None, TI=
2025-05-25 13:23:03 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT] Valores salvos: NBI=None, SIL=None, TA=None, TI=/perdas
2025-05-25 13:23:03 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT-NEUTRO] Acionado por: url. Norma: IEC, Um_Neutro: None, Conexão: triangulo, Tensão: 69
2025-05-25 13:23:03 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO] Valores do MCP: NBI=None, SIL=None, TA=None, TI=None
2025-05-25 13:23:03 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT-NEUTRO] Acionado por: url. Norma: IEC, Um_Neutro: None, Conexão: triangulo, Tensão: 13.8
2025-05-25 13:23:03 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT-NEUTRO] Valores do MCP: NBI=None, SIL=None
2025-05-25 13:23:03 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO-NEUTRO] Acionado por: url. Norma: IEC, Um_Neutro: None, Conexão: triangulo, Tensão: 13
2025-05-25 13:23:03 - app_core.isolation_repo - WARNING - [ISOLATION] Nenhum registro encontrado para Um=69.0kV e norma=IEC/NBR. Usando valores padrão.
2025-05-25 13:23:03 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO] Valores salvos: NBI=None, SIL=None, TA=None, TI=/perdas
2025-05-25 13:23:03 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - BT] Opções FILTRADAS por Um=3.6 e Norma=IEC.
2025-05-25 13:23:03 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT-NEUTRO] Valores do MCP: NBI=None, SIL=None
2025-05-25 13:23:03 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT-NEUTRO] Valores salvos: NBI=None, SIL=None
2025-05-25 13:23:03 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO-NEUTRO] Valores do MCP: NBI=None, SIL=None
2025-05-25 13:23:03 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - AT] Opções FILTRADAS por Um=69.0 e Norma=IEC.
2025-05-25 13:23:03 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT-NEUTRO] Valores salvos: NBI=None, SIL=None
2025-05-25 13:23:03 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - TERCIARIO] Opções FILTRADAS por Um=24.0 e Norma=IEC.
2025-05-25 13:23:03 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO-NEUTRO] Valores salvos: NBI=None, SIL=None
2025-05-25 13:23:03 - callbacks.global_updates - WARNING - Dados essenciais ausentes no transformer_store_data
2025-05-25 13:23:03 - components.transformer_info_template - WARNING - transformer_data inválido ou vazio: {}
2025-05-25 13:23:03 - callbacks.global_updates - DEBUG - Painel atualizado em 2.25ms
2025-05-25 13:23:03 - callbacks.losses - CRITICAL - [LOSSES POPULATE CARGA] Acionado. Aba: tab-carga, Trigger: N/A
2025-05-25 13:23:05 - layouts.impulse - INFO - Criando layout de impulso...
2025-05-25 13:23:05 - layouts.impulse - INFO - [Impulse] Dados do transformador obtidos do MCP: 58
2025-05-25 13:23:05 - callbacks.global_updates - WARNING - Dados essenciais ausentes no transformer_store_data
2025-05-25 13:23:05 - components.transformer_info_template - WARNING - transformer_data inválido ou vazio: {}
2025-05-25 13:23:05 - callbacks.global_updates - DEBUG - Painel atualizado em 0.0ms
2025-05-25 13:23:05 - layouts.dieletric_analysis - INFO - Creating Dielectric Analysis layout (from dieletric_analysis.py)...
2025-05-25 13:23:05 - app_core.standards - ERROR - Arquivo JSON 'c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\tabela.json' não encontrado.
2025-05-25 13:23:05 - app_core.standards - ERROR - Arquivo JSON 'c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\tabela.json' não encontrado.
2025-05-25 13:23:05 - app_core.standards - ERROR - Arquivo JSON 'c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\tabela.json' não encontrado.
2025-05-25 13:23:05 - app_core.standards - ERROR - Arquivo JSON 'c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\tabela.json' não encontrado.
2025-05-25 13:23:05 - app_core.standards - CRITICAL - Falha CRÍTICA ao inicializar as classes das normas: Falha ao carregar dados JSON para NBR ou IEEE.
Traceback (most recent call last):
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\app_core\standards.py", line 613, in __init__
    raise RuntimeError("Falha ao carregar dados JSON para NBR ou IEEE.")
RuntimeError: Falha ao carregar dados JSON para NBR ou IEEE.
2025-05-25 13:23:05 - layouts.dieletric_analysis - WARNING - VerificadorTransformador is invalid upon creation in create_dielectric_layout.
2025-05-25 13:23:05 - components.ui_elements - WARNING - Verificador inválido ao criar coluna dielétrica (Alta Tensão).
2025-05-25 13:23:05 - components.ui_elements - WARNING - Verificador inválido ao criar coluna dielétrica (Baixa Tensão).
2025-05-25 13:23:05 - components.ui_elements - WARNING - Verificador inválido ao criar coluna dielétrica (Terciário).
2025-05-25 13:23:05 - callbacks.global_updates - WARNING - Dados essenciais ausentes no transformer_store_data
2025-05-25 13:23:05 - components.transformer_info_template - WARNING - transformer_data inválido ou vazio: {}
2025-05-25 13:23:05 - app_core.standards - ERROR - Arquivo JSON 'c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\tabela.json' não encontrado.
2025-05-25 13:23:05 - callbacks.global_updates - DEBUG - Painel atualizado em 1.0ms
2025-05-25 13:23:05 - app_core.standards - ERROR - Arquivo JSON 'c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\tabela.json' não encontrado.
2025-05-25 13:23:05 - app_core.standards - ERROR - Arquivo JSON 'c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\tabela.json' não encontrado.
2025-05-25 13:23:05 - app_core.standards - ERROR - Arquivo JSON 'c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\tabela.json' não encontrado.
2025-05-25 13:23:05 - app_core.standards - CRITICAL - Falha CRÍTICA ao inicializar as classes das normas: Falha ao carregar dados JSON para NBR ou IEEE.
Traceback (most recent call last):
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\app_core\standards.py", line 613, in __init__
    raise RuntimeError("Falha ao carregar dados JSON para NBR ou IEEE.")
RuntimeError: Falha ao carregar dados JSON para NBR ou IEEE.
2025-05-25 13:23:05 - callbacks.dieletric_analysis - ERROR - Instância VerificadorTransformador criada, mas inválida (falha dados?).
2025-05-25 13:23:06 - layouts.applied_voltage - INFO - [Applied Voltage Layout] Creating layout...
2025-05-25 13:23:06 - callbacks.global_updates - WARNING - Dados essenciais ausentes no transformer_store_data
2025-05-25 13:23:06 - components.transformer_info_template - WARNING - transformer_data inválido ou vazio: {}
2025-05-25 13:23:06 - app - ERROR - Exception on /_dash-update-component [POST]
Traceback (most recent call last):
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\flask\app.py", line 2190, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\flask\app.py", line 1486, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\dash\dash.py", line 1373, in dispatch
    ctx.run(
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\dash\_callback.py", line 465, in add_context
    output_value = _invoke_callback(func, *func_args, **func_kwargs)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\dash\_callback.py", line 40, in _invoke_callback
    return func(*args, **kwargs)  # %% callback invoked %%
           ^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\callbacks\applied_voltage.py", line 821, in load_applied_voltage_inputs
    raise dash.no_update
TypeError: exceptions must derive from BaseException
2025-05-25 13:23:06 - callbacks.global_updates - DEBUG - Painel atualizado em 0.0ms
2025-05-25 13:23:06 - app - ERROR - Exception on /_dash-update-component [POST]
Traceback (most recent call last):
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\flask\app.py", line 2190, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\flask\app.py", line 1486, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\dash\dash.py", line 1373, in dispatch
    ctx.run(
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\dash\_callback.py", line 465, in add_context
    output_value = _invoke_callback(func, *func_args, **func_kwargs)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\dash\_callback.py", line 40, in _invoke_callback
    return func(*args, **kwargs)  # %% callback invoked %%
           ^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\callbacks\applied_voltage.py", line 784, in load_applied_voltage_inputs
    raise dash.no_update
TypeError: exceptions must derive from BaseException
2025-05-25 13:23:06 - layouts.transformer_inputs - INFO - Criando layout Dados Básicos (v5 - Layout Geral e Pesos em 2 linhas)...
2025-05-25 13:23:06 - layouts.transformer_inputs - INFO - Classes de tensão carregadas para dropdown: 27 opções.
2025-05-25 13:23:07 - callbacks.global_updates - WARNING - Dados essenciais ausentes no transformer_store_data
2025-05-25 13:23:07 - components.transformer_info_template - WARNING - transformer_data inválido ou vazio: {}
2025-05-25 13:23:07 - callbacks.global_updates - DEBUG - Painel atualizado em 0.0ms
2025-05-25 13:23:07 - callbacks.transformer_inputs - INFO - [LOAD TransformerInputs] Acionado por: None
2025-05-25 13:23:07 - callbacks.transformer_inputs - INFO - [LOAD TransformerInputs] Opções dos dropdowns ainda não foram populadas, aguardando...
2025-05-25 13:23:07 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_kv: 69, Tensão: 69, Path: /dados
2025-05-25 13:23:07 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT-NEUTRO] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_Neutro: None, Conexão: triangulo, Tensão: 69
2025-05-25 13:23:07 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT] Valores do MCP: NBI=None, SIL=None, TA=None, TI=None
2025-05-25 13:23:07 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_kv: 3.6, Tensão: 13.8, Path: /dados
2025-05-25 13:23:07 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT-NEUTRO] Valores do MCP: NBI=None, SIL=None
2025-05-25 13:23:07 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT-NEUTRO] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_Neutro: None, Conexão: triangulo, Tensão: 13.8
2025-05-25 13:23:07 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT] Valores salvos: NBI=None, SIL=None, TA=None, TI=
2025-05-25 13:23:07 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_kv: 24, Tensão: 13, Path: /dados
2025-05-25 13:23:07 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT] Valores do MCP: NBI=None, SIL=None, TA=None, TI=None
2025-05-25 13:23:07 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO-NEUTRO] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_Neutro: None, Conexão: triangulo, Tensão: 13
2025-05-25 13:23:07 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT-NEUTRO] Valores salvos: NBI=None, SIL=None
2025-05-25 13:23:07 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT-NEUTRO] Valores do MCP: NBI=None, SIL=None
2025-05-25 13:23:07 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO] Valores do MCP: NBI=None, SIL=None, TA=None, TI=None
2025-05-25 13:23:07 - app_core.isolation_repo - WARNING - [ISOLATION] Nenhum registro encontrado para Um=69.0kV e norma=IEC/NBR. Usando valores padrão.
2025-05-25 13:23:07 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT] Valores salvos: NBI=None, SIL=None, TA=None, TI=/dados
2025-05-25 13:23:07 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO-NEUTRO] Valores do MCP: NBI=None, SIL=None
2025-05-25 13:23:07 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT-NEUTRO] Valores salvos: NBI=None, SIL=None
2025-05-25 13:23:07 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO] Valores salvos: NBI=None, SIL=None, TA=None, TI=/dados
2025-05-25 13:23:07 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - AT] Opções FILTRADAS por Um=69.0 e Norma=IEC.
2025-05-25 13:23:07 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO-NEUTRO] Valores salvos: NBI=None, SIL=None
2025-05-25 13:23:07 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - BT] Opções FILTRADAS por Um=3.6 e Norma=IEC.
2025-05-25 13:23:07 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - TERCIARIO] Opções FILTRADAS por Um=24.0 e Norma=IEC.
2025-05-25 13:23:09 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT] Acionado por: tensao_at. Norma: IEC, Um_kv: 69, Tensão: 69.1, Path: /dados
2025-05-25 13:23:09 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT] Valores do MCP: NBI=None, SIL=None, TA=None, TI=None
2025-05-25 13:23:09 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT-NEUTRO] Acionado por: tensao_at. Norma: IEC, Um_Neutro: None, Conexão: triangulo, Tensão: 69.1
2025-05-25 13:23:09 - callbacks.transformer_inputs - DEBUG - [UpdateTransformerCalc] Acionado por: tensao_at
2025-05-25 13:23:09 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT] Valores salvos: NBI=None, SIL=None, TA=None, TI=
2025-05-25 13:23:09 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT-NEUTRO] Valores do MCP: NBI=None, SIL=None
2025-05-25 13:23:09 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT-NEUTRO] Valores salvos: NBI=None, SIL=None
2025-05-25 13:23:09 - app_core.isolation_repo - WARNING - [ISOLATION] Nenhum registro encontrado para Um=69.0kV e norma=IEC/NBR. Usando valores padrão.
2025-05-25 13:23:09 - callbacks.transformer_inputs - INFO - [UpdateTransformerCalc] Sincronizando elevação de enrolamento: AT=BT=Terciário=55°C
2025-05-25 13:23:09 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - AT] Opções FILTRADAS por Um=69.0 e Norma=IEC.
2025-05-25 13:23:09 - app_core.transformer_mcp - DEBUG - [MCP SET] Dados definidos para store 'transformer-inputs-store'.
2025-05-25 13:23:09 - app_core.transformer_mcp - INFO - [MCP SAVE_TO_DISK] Salvando estado do MCP em disco...
2025-05-25 13:23:09 - app_core.transformer_mcp - INFO - [MCP SAVE_TO_DISK] Estado do MCP salvo em disco com sucesso.
2025-05-25 13:23:09 - callbacks.transformer_inputs - DEBUG - [UpdateTransformerCalc] MCP atualizado e salvo no disco.
2025-05-25 13:23:09 - callbacks.transformer_inputs - DEBUG - [UpdateTransformerCalc] Dados propagados para todos os stores alvo.
2025-05-25 13:23:14 - callbacks.transformer_inputs - INFO - [LOAD TransformerInputs] Acionado por: url
2025-05-25 13:23:14 - callbacks.transformer_inputs - DEBUG - [LOAD TransformerInputs] Não estamos na página de dados (pathname=/perdas, clean_path=perdas), prevenindo atualização
2025-05-25 13:23:14 - utils.mcp_persistence - INFO - [ensure_mcp_data_propagation] Store de origem transformer-inputs-store É a fonte-de-verdade para campos básicos
2025-05-25 13:23:14 - app_core.transformer_mcp - DEBUG - [MCP SET] Dados definidos para store 'losses-store'.
2025-05-25 13:23:14 - utils.mcp_persistence - INFO - [ensure_mcp_data_propagation] Dados básicos propagados para losses-store
2025-05-25 13:23:14 - layouts.losses - INFO - Creating Losses layout...
2025-05-25 13:23:14 - layouts.losses - INFO - [Losses Layout] Dados do transformador obtidos do losses-store.
2025-05-25 13:23:14 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT] Acionado por: url. Norma: IEC, Um_kv: 69, Tensão: 69.1, Path: /perdas
2025-05-25 13:23:14 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT] Acionado por: url. Norma: IEC, Um_kv: 3.6, Tensão: 13.8, Path: /perdas
2025-05-25 13:23:14 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT] Valores do MCP: NBI=None, SIL=None, TA=None, TI=None
2025-05-25 13:23:14 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO] Acionado por: url. Norma: IEC, Um_kv: 24, Tensão: 13, Path: /perdas
2025-05-25 13:23:14 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT] Valores do MCP: NBI=None, SIL=None, TA=None, TI=None
2025-05-25 13:23:14 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT] Valores salvos: NBI=None, SIL=None, TA=None, TI=
2025-05-25 13:23:14 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO] Valores do MCP: NBI=None, SIL=None, TA=None, TI=None
2025-05-25 13:23:14 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT-NEUTRO] Acionado por: url. Norma: IEC, Um_Neutro: None, Conexão: triangulo, Tensão: 69.1
2025-05-25 13:23:14 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT] Valores salvos: NBI=None, SIL=None, TA=None, TI=/perdas
2025-05-25 13:23:14 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO] Valores salvos: NBI=None, SIL=None, TA=None, TI=/perdas
2025-05-25 13:23:14 - app_core.isolation_repo - WARNING - [ISOLATION] Nenhum registro encontrado para Um=69.0kV e norma=IEC/NBR. Usando valores padrão.
2025-05-25 13:23:14 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT-NEUTRO] Acionado por: url. Norma: IEC, Um_Neutro: None, Conexão: triangulo, Tensão: 13.8
2025-05-25 13:23:14 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT-NEUTRO] Valores do MCP: NBI=None, SIL=None
2025-05-25 13:23:14 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO-NEUTRO] Acionado por: url. Norma: IEC, Um_Neutro: None, Conexão: triangulo, Tensão: 13
2025-05-25 13:23:14 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - BT] Opções FILTRADAS por Um=3.6 e Norma=IEC.
2025-05-25 13:23:14 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - AT] Opções FILTRADAS por Um=69.0 e Norma=IEC.
2025-05-25 13:23:14 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT-NEUTRO] Valores do MCP: NBI=None, SIL=None
2025-05-25 13:23:14 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT-NEUTRO] Valores salvos: NBI=None, SIL=None
2025-05-25 13:23:14 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO-NEUTRO] Valores do MCP: NBI=None, SIL=None
2025-05-25 13:23:14 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - TERCIARIO] Opções FILTRADAS por Um=24.0 e Norma=IEC.
2025-05-25 13:23:14 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT-NEUTRO] Valores salvos: NBI=None, SIL=None
2025-05-25 13:23:14 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO-NEUTRO] Valores salvos: NBI=None, SIL=None
2025-05-25 13:23:14 - callbacks.global_updates - WARNING - Dados essenciais ausentes no transformer_store_data
2025-05-25 13:23:14 - components.transformer_info_template - WARNING - transformer_data inválido ou vazio: {}
2025-05-25 13:23:14 - callbacks.global_updates - DEBUG - Painel atualizado em 2.51ms
2025-05-25 13:23:14 - callbacks.losses - CRITICAL - [LOSSES POPULATE CARGA] Acionado. Aba: tab-carga, Trigger: N/A
2025-05-25 13:23:15 - layouts.impulse - INFO - Criando layout de impulso...
2025-05-25 13:23:15 - layouts.impulse - INFO - [Impulse] Dados do transformador obtidos do MCP: 58
2025-05-25 13:23:15 - callbacks.global_updates - WARNING - Dados essenciais ausentes no transformer_store_data
2025-05-25 13:23:15 - components.transformer_info_template - WARNING - transformer_data inválido ou vazio: {}
2025-05-25 13:23:15 - callbacks.global_updates - DEBUG - Painel atualizado em 0.0ms
2025-05-25 13:23:15 - layouts.transformer_inputs - INFO - Criando layout Dados Básicos (v5 - Layout Geral e Pesos em 2 linhas)...
2025-05-25 13:23:15 - layouts.transformer_inputs - INFO - Classes de tensão carregadas para dropdown: 27 opções.
2025-05-25 13:23:15 - callbacks.global_updates - WARNING - Dados essenciais ausentes no transformer_store_data
2025-05-25 13:23:15 - components.transformer_info_template - WARNING - transformer_data inválido ou vazio: {}
2025-05-25 13:23:15 - callbacks.global_updates - DEBUG - Painel atualizado em 1.0ms
2025-05-25 13:23:15 - callbacks.transformer_inputs - INFO - [LOAD TransformerInputs] Acionado por: None
2025-05-25 13:23:15 - callbacks.transformer_inputs - INFO - [LOAD TransformerInputs] Opções dos dropdowns ainda não foram populadas, aguardando...
2025-05-25 13:23:15 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_kv: 69, Tensão: 69.1, Path: /dados
2025-05-25 13:23:15 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT-NEUTRO] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_Neutro: None, Conexão: triangulo, Tensão: 69.1
2025-05-25 13:23:15 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT] Valores do MCP: NBI=None, SIL=None, TA=None, TI=None
2025-05-25 13:23:15 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_kv: 3.6, Tensão: 13.8, Path: /dados
2025-05-25 13:23:15 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT-NEUTRO] Valores do MCP: NBI=None, SIL=None
2025-05-25 13:23:15 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT-NEUTRO] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_Neutro: None, Conexão: triangulo, Tensão: 13.8
2025-05-25 13:23:15 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT] Valores salvos: NBI=None, SIL=None, TA=None, TI=
2025-05-25 13:23:15 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT] Valores do MCP: NBI=None, SIL=None, TA=None, TI=None
2025-05-25 13:23:15 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_kv: 24, Tensão: 13, Path: /dados
2025-05-25 13:23:15 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT-NEUTRO] Valores salvos: NBI=None, SIL=None
2025-05-25 13:23:15 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO-NEUTRO] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_Neutro: None, Conexão: triangulo, Tensão: 13
2025-05-25 13:23:15 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT-NEUTRO] Valores do MCP: NBI=None, SIL=None
2025-05-25 13:23:15 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT] Valores salvos: NBI=None, SIL=None, TA=None, TI=/dados
2025-05-25 13:23:15 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO] Valores do MCP: NBI=None, SIL=None, TA=None, TI=None
2025-05-25 13:23:15 - app_core.isolation_repo - WARNING - [ISOLATION] Nenhum registro encontrado para Um=69.0kV e norma=IEC/NBR. Usando valores padrão.
2025-05-25 13:23:15 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO-NEUTRO] Valores do MCP: NBI=None, SIL=None
2025-05-25 13:23:15 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT-NEUTRO] Valores salvos: NBI=None, SIL=None
2025-05-25 13:23:15 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO] Valores salvos: NBI=None, SIL=None, TA=None, TI=/dados
2025-05-25 13:23:15 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - AT] Opções FILTRADAS por Um=69.0 e Norma=IEC.
2025-05-25 13:23:15 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO-NEUTRO] Valores salvos: NBI=None, SIL=None
2025-05-25 13:23:15 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - BT] Opções FILTRADAS por Um=3.6 e Norma=IEC.
2025-05-25 13:23:15 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - TERCIARIO] Opções FILTRADAS por Um=24.0 e Norma=IEC.
2025-05-25 13:23:18 - callbacks.transformer_inputs - DEBUG - [UpdateTransformerCalc] Acionado por: tensao_at_tap_maior
2025-05-25 13:23:18 - callbacks.transformer_inputs - INFO - [UpdateTransformerCalc] Sincronizando elevação de enrolamento: AT=BT=Terciário=55°C
2025-05-25 13:23:18 - app_core.transformer_mcp - DEBUG - [MCP SET] Dados definidos para store 'transformer-inputs-store'.
2025-05-25 13:23:18 - app_core.transformer_mcp - INFO - [MCP SAVE_TO_DISK] Salvando estado do MCP em disco...
2025-05-25 13:23:18 - app_core.transformer_mcp - INFO - [MCP SAVE_TO_DISK] Estado do MCP salvo em disco com sucesso.
2025-05-25 13:23:18 - callbacks.transformer_inputs - DEBUG - [UpdateTransformerCalc] MCP atualizado e salvo no disco.
2025-05-25 13:23:18 - callbacks.transformer_inputs - DEBUG - [UpdateTransformerCalc] Dados propagados para todos os stores alvo.
2025-05-25 13:23:22 - callbacks.transformer_inputs - DEBUG - [UpdateTransformerCalc] Acionado por: nbi_at
2025-05-25 13:23:22 - callbacks.transformer_inputs - INFO - [UpdateTransformerCalc] Salvando valor de isolamento: nbi_at=325
2025-05-25 13:23:22 - callbacks.transformer_inputs - INFO - [UpdateTransformerCalc] Sincronizando elevação de enrolamento: AT=BT=Terciário=55°C
2025-05-25 13:23:22 - app_core.transformer_mcp - DEBUG - [MCP SET] Dados definidos para store 'transformer-inputs-store'.
2025-05-25 13:23:22 - app_core.transformer_mcp - INFO - [MCP SAVE_TO_DISK] Salvando estado do MCP em disco...
2025-05-25 13:23:22 - app_core.transformer_mcp - INFO - [MCP SAVE_TO_DISK] Estado do MCP salvo em disco com sucesso.
2025-05-25 13:23:22 - callbacks.transformer_inputs - DEBUG - [UpdateTransformerCalc] MCP atualizado e salvo no disco.
2025-05-25 13:23:22 - callbacks.transformer_inputs - DEBUG - [UpdateTransformerCalc] Dados propagados para todos os stores alvo.
2025-05-25 13:23:23 - callbacks.transformer_inputs - DEBUG - [UpdateTransformerCalc] Acionado por: sil_at
2025-05-25 13:23:23 - callbacks.transformer_inputs - INFO - [UpdateTransformerCalc] Salvando valor de isolamento: nbi_at=325
2025-05-25 13:23:23 - callbacks.transformer_inputs - INFO - [UpdateTransformerCalc] Salvando valor de isolamento: sil_at=NA_SIL
2025-05-25 13:23:23 - callbacks.transformer_inputs - INFO - [UpdateTransformerCalc] Sincronizando elevação de enrolamento: AT=BT=Terciário=55°C
2025-05-25 13:23:23 - app_core.transformer_mcp - DEBUG - [MCP SET] Dados definidos para store 'transformer-inputs-store'.
2025-05-25 13:23:23 - app_core.transformer_mcp - INFO - [MCP SAVE_TO_DISK] Salvando estado do MCP em disco...
2025-05-25 13:23:23 - app_core.transformer_mcp - INFO - [MCP SAVE_TO_DISK] Estado do MCP salvo em disco com sucesso.
2025-05-25 13:23:23 - callbacks.transformer_inputs - DEBUG - [UpdateTransformerCalc] MCP atualizado e salvo no disco.
2025-05-25 13:23:23 - callbacks.transformer_inputs - DEBUG - [UpdateTransformerCalc] Dados propagados para todos os stores alvo.
2025-05-25 13:23:23 - callbacks.transformer_inputs - DEBUG - [UpdateTransformerCalc] Acionado por: nbi_bt
2025-05-25 13:23:23 - callbacks.transformer_inputs - INFO - [UpdateTransformerCalc] Salvando valor de isolamento: nbi_at=325
2025-05-25 13:23:23 - callbacks.transformer_inputs - INFO - [UpdateTransformerCalc] Salvando valor de isolamento: sil_at=NA_SIL
2025-05-25 13:23:23 - callbacks.transformer_inputs - INFO - [UpdateTransformerCalc] Sincronizando elevação de enrolamento: AT=BT=Terciário=55°C
2025-05-25 13:23:23 - app_core.transformer_mcp - DEBUG - [MCP SET] Dados definidos para store 'transformer-inputs-store'.
2025-05-25 13:23:23 - app_core.transformer_mcp - INFO - [MCP SAVE_TO_DISK] Salvando estado do MCP em disco...
2025-05-25 13:23:23 - app_core.transformer_mcp - INFO - [MCP SAVE_TO_DISK] Estado do MCP salvo em disco com sucesso.
2025-05-25 13:23:23 - callbacks.transformer_inputs - DEBUG - [UpdateTransformerCalc] MCP atualizado e salvo no disco.
2025-05-25 13:23:23 - callbacks.transformer_inputs - DEBUG - [UpdateTransformerCalc] Dados propagados para todos os stores alvo.
2025-05-25 13:23:25 - callbacks.transformer_inputs - DEBUG - [UpdateTransformerCalc] Acionado por: sil_bt
2025-05-25 13:23:25 - callbacks.transformer_inputs - INFO - [UpdateTransformerCalc] Salvando valor de isolamento: nbi_at=325
2025-05-25 13:23:25 - callbacks.transformer_inputs - INFO - [UpdateTransformerCalc] Salvando valor de isolamento: sil_at=NA_SIL
2025-05-25 13:23:25 - callbacks.transformer_inputs - INFO - [UpdateTransformerCalc] Sincronizando elevação de enrolamento: AT=BT=Terciário=55°C
2025-05-25 13:23:25 - app_core.transformer_mcp - DEBUG - [MCP SET] Dados definidos para store 'transformer-inputs-store'.
2025-05-25 13:23:25 - app_core.transformer_mcp - INFO - [MCP SAVE_TO_DISK] Salvando estado do MCP em disco...
2025-05-25 13:23:25 - app_core.transformer_mcp - INFO - [MCP SAVE_TO_DISK] Estado do MCP salvo em disco com sucesso.
2025-05-25 13:23:25 - callbacks.transformer_inputs - DEBUG - [UpdateTransformerCalc] MCP atualizado e salvo no disco.
2025-05-25 13:23:25 - callbacks.transformer_inputs - DEBUG - [UpdateTransformerCalc] Dados propagados para todos os stores alvo.
2025-05-25 13:23:26 - callbacks.transformer_inputs - DEBUG - [UpdateTransformerCalc] Acionado por: nbi_terciario
2025-05-25 13:23:26 - callbacks.transformer_inputs - INFO - [UpdateTransformerCalc] Salvando valor de isolamento: nbi_at=325
2025-05-25 13:23:26 - callbacks.transformer_inputs - INFO - [UpdateTransformerCalc] Salvando valor de isolamento: sil_at=NA_SIL
2025-05-25 13:23:26 - callbacks.transformer_inputs - INFO - [UpdateTransformerCalc] Sincronizando elevação de enrolamento: AT=BT=Terciário=55°C
2025-05-25 13:23:26 - app_core.transformer_mcp - DEBUG - [MCP SET] Dados definidos para store 'transformer-inputs-store'.
2025-05-25 13:23:26 - app_core.transformer_mcp - INFO - [MCP SAVE_TO_DISK] Salvando estado do MCP em disco...
2025-05-25 13:23:26 - app_core.transformer_mcp - INFO - [MCP SAVE_TO_DISK] Estado do MCP salvo em disco com sucesso.
2025-05-25 13:23:26 - callbacks.transformer_inputs - DEBUG - [UpdateTransformerCalc] MCP atualizado e salvo no disco.
2025-05-25 13:23:26 - callbacks.transformer_inputs - DEBUG - [UpdateTransformerCalc] Dados propagados para todos os stores alvo.
2025-05-25 13:23:26 - callbacks.transformer_inputs - DEBUG - [UpdateTransformerCalc] Acionado por: sil_terciario
2025-05-25 13:23:26 - callbacks.transformer_inputs - INFO - [UpdateTransformerCalc] Salvando valor de isolamento: nbi_at=325
2025-05-25 13:23:26 - callbacks.transformer_inputs - INFO - [UpdateTransformerCalc] Salvando valor de isolamento: sil_at=NA_SIL
2025-05-25 13:23:26 - callbacks.transformer_inputs - INFO - [UpdateTransformerCalc] Sincronizando elevação de enrolamento: AT=BT=Terciário=55°C
2025-05-25 13:23:26 - app_core.transformer_mcp - DEBUG - [MCP SET] Dados definidos para store 'transformer-inputs-store'.
2025-05-25 13:23:26 - app_core.transformer_mcp - INFO - [MCP SAVE_TO_DISK] Salvando estado do MCP em disco...
2025-05-25 13:23:26 - app_core.transformer_mcp - INFO - [MCP SAVE_TO_DISK] Estado do MCP salvo em disco com sucesso.
2025-05-25 13:23:26 - callbacks.transformer_inputs - DEBUG - [UpdateTransformerCalc] MCP atualizado e salvo no disco.
2025-05-25 13:23:26 - callbacks.transformer_inputs - DEBUG - [UpdateTransformerCalc] Dados propagados para todos os stores alvo.
2025-05-25 13:23:29 - callbacks.transformer_inputs - DEBUG - [UpdateTransformerCalc] Acionado por: nbi_neutro_terciario
2025-05-25 13:23:29 - callbacks.transformer_inputs - INFO - [UpdateTransformerCalc] Salvando valor de isolamento: nbi_at=325
2025-05-25 13:23:29 - callbacks.transformer_inputs - INFO - [UpdateTransformerCalc] Salvando valor de isolamento: sil_at=NA_SIL
2025-05-25 13:23:29 - callbacks.transformer_inputs - INFO - [UpdateTransformerCalc] Sincronizando elevação de enrolamento: AT=BT=Terciário=55°C
2025-05-25 13:23:29 - app_core.transformer_mcp - DEBUG - [MCP SET] Dados definidos para store 'transformer-inputs-store'.
2025-05-25 13:23:29 - app_core.transformer_mcp - INFO - [MCP SAVE_TO_DISK] Salvando estado do MCP em disco...
2025-05-25 13:23:29 - app_core.transformer_mcp - INFO - [MCP SAVE_TO_DISK] Estado do MCP salvo em disco com sucesso.
2025-05-25 13:23:29 - callbacks.transformer_inputs - DEBUG - [UpdateTransformerCalc] MCP atualizado e salvo no disco.
2025-05-25 13:23:29 - callbacks.transformer_inputs - DEBUG - [UpdateTransformerCalc] Dados propagados para todos os stores alvo.
2025-05-25 13:23:30 - callbacks.transformer_inputs - DEBUG - [UpdateTransformerCalc] Acionado por: sil_neutro_terciario
2025-05-25 13:23:30 - callbacks.transformer_inputs - INFO - [UpdateTransformerCalc] Salvando valor de isolamento: nbi_at=325
2025-05-25 13:23:30 - callbacks.transformer_inputs - INFO - [UpdateTransformerCalc] Salvando valor de isolamento: sil_at=NA_SIL
2025-05-25 13:23:30 - callbacks.transformer_inputs - INFO - [UpdateTransformerCalc] Sincronizando elevação de enrolamento: AT=BT=Terciário=55°C
2025-05-25 13:23:30 - app_core.transformer_mcp - DEBUG - [MCP SET] Dados definidos para store 'transformer-inputs-store'.
2025-05-25 13:23:30 - app_core.transformer_mcp - INFO - [MCP SAVE_TO_DISK] Salvando estado do MCP em disco...
2025-05-25 13:23:30 - app_core.transformer_mcp - INFO - [MCP SAVE_TO_DISK] Estado do MCP salvo em disco com sucesso.
2025-05-25 13:23:30 - callbacks.transformer_inputs - DEBUG - [UpdateTransformerCalc] MCP atualizado e salvo no disco.
2025-05-25 13:23:30 - callbacks.transformer_inputs - DEBUG - [UpdateTransformerCalc] Dados propagados para todos os stores alvo.
2025-05-25 13:23:31 - callbacks.transformer_inputs - DEBUG - [UpdateTransformerCalc] Acionado por: sil_neutro_bt
2025-05-25 13:23:31 - callbacks.transformer_inputs - INFO - [UpdateTransformerCalc] Salvando valor de isolamento: nbi_at=325
2025-05-25 13:23:31 - callbacks.transformer_inputs - INFO - [UpdateTransformerCalc] Salvando valor de isolamento: sil_at=NA_SIL
2025-05-25 13:23:31 - callbacks.transformer_inputs - INFO - [UpdateTransformerCalc] Sincronizando elevação de enrolamento: AT=BT=Terciário=55°C
2025-05-25 13:23:31 - app_core.transformer_mcp - DEBUG - [MCP SET] Dados definidos para store 'transformer-inputs-store'.
2025-05-25 13:23:31 - app_core.transformer_mcp - INFO - [MCP SAVE_TO_DISK] Salvando estado do MCP em disco...
2025-05-25 13:23:31 - app_core.transformer_mcp - INFO - [MCP SAVE_TO_DISK] Estado do MCP salvo em disco com sucesso.
2025-05-25 13:23:31 - callbacks.transformer_inputs - DEBUG - [UpdateTransformerCalc] MCP atualizado e salvo no disco.
2025-05-25 13:23:31 - callbacks.transformer_inputs - DEBUG - [UpdateTransformerCalc] Dados propagados para todos os stores alvo.
2025-05-25 13:23:32 - callbacks.transformer_inputs - DEBUG - [UpdateTransformerCalc] Acionado por: sil_neutro_at
2025-05-25 13:23:32 - callbacks.transformer_inputs - INFO - [UpdateTransformerCalc] Salvando valor de isolamento: nbi_at=325
2025-05-25 13:23:32 - callbacks.transformer_inputs - INFO - [UpdateTransformerCalc] Salvando valor de isolamento: sil_at=NA_SIL
2025-05-25 13:23:32 - callbacks.transformer_inputs - INFO - [UpdateTransformerCalc] Sincronizando elevação de enrolamento: AT=BT=Terciário=55°C
2025-05-25 13:23:32 - app_core.transformer_mcp - DEBUG - [MCP SET] Dados definidos para store 'transformer-inputs-store'.
2025-05-25 13:23:32 - app_core.transformer_mcp - INFO - [MCP SAVE_TO_DISK] Salvando estado do MCP em disco...
2025-05-25 13:23:32 - app_core.transformer_mcp - INFO - [MCP SAVE_TO_DISK] Estado do MCP salvo em disco com sucesso.
2025-05-25 13:23:32 - callbacks.transformer_inputs - DEBUG - [UpdateTransformerCalc] MCP atualizado e salvo no disco.
2025-05-25 13:23:32 - callbacks.transformer_inputs - DEBUG - [UpdateTransformerCalc] Dados propagados para todos os stores alvo.
2025-05-25 13:23:33 - callbacks.transformer_inputs - DEBUG - [UpdateTransformerCalc] Acionado por: nbi_neutro_at
2025-05-25 13:23:33 - callbacks.transformer_inputs - INFO - [UpdateTransformerCalc] Salvando valor de isolamento: nbi_at=325
2025-05-25 13:23:33 - callbacks.transformer_inputs - INFO - [UpdateTransformerCalc] Salvando valor de isolamento: sil_at=NA_SIL
2025-05-25 13:23:33 - callbacks.transformer_inputs - INFO - [UpdateTransformerCalc] Sincronizando elevação de enrolamento: AT=BT=Terciário=55°C
2025-05-25 13:23:33 - app_core.transformer_mcp - DEBUG - [MCP SET] Dados definidos para store 'transformer-inputs-store'.
2025-05-25 13:23:33 - app_core.transformer_mcp - INFO - [MCP SAVE_TO_DISK] Salvando estado do MCP em disco...
2025-05-25 13:23:33 - app_core.transformer_mcp - INFO - [MCP SAVE_TO_DISK] Estado do MCP salvo em disco com sucesso.
2025-05-25 13:23:33 - callbacks.transformer_inputs - DEBUG - [UpdateTransformerCalc] MCP atualizado e salvo no disco.
2025-05-25 13:23:33 - callbacks.transformer_inputs - DEBUG - [UpdateTransformerCalc] Dados propagados para todos os stores alvo.
2025-05-25 13:23:35 - callbacks.transformer_inputs - DEBUG - [UpdateTransformerCalc] Acionado por: teste_tensao_aplicada_at
2025-05-25 13:23:35 - callbacks.transformer_inputs - INFO - [UpdateTransformerCalc] Salvando valor de isolamento: nbi_at=325
2025-05-25 13:23:35 - callbacks.transformer_inputs - INFO - [UpdateTransformerCalc] Salvando valor de isolamento: sil_at=NA_SIL
2025-05-25 13:23:35 - callbacks.transformer_inputs - INFO - [UpdateTransformerCalc] Salvando valor de isolamento: teste_tensao_aplicada_at=140
2025-05-25 13:23:35 - callbacks.transformer_inputs - INFO - [UpdateTransformerCalc] Sincronizando elevação de enrolamento: AT=BT=Terciário=55°C
2025-05-25 13:23:35 - app_core.transformer_mcp - DEBUG - [MCP SET] Dados definidos para store 'transformer-inputs-store'.
2025-05-25 13:23:35 - app_core.transformer_mcp - INFO - [MCP SAVE_TO_DISK] Salvando estado do MCP em disco...
2025-05-25 13:23:35 - app_core.transformer_mcp - INFO - [MCP SAVE_TO_DISK] Estado do MCP salvo em disco com sucesso.
2025-05-25 13:23:35 - callbacks.transformer_inputs - DEBUG - [UpdateTransformerCalc] MCP atualizado e salvo no disco.
2025-05-25 13:23:35 - callbacks.transformer_inputs - DEBUG - [UpdateTransformerCalc] Dados propagados para todos os stores alvo.
2025-05-25 13:23:38 - callbacks.transformer_inputs - DEBUG - [UpdateTransformerCalc] Acionado por: teste_tensao_aplicada_bt
2025-05-25 13:23:38 - callbacks.transformer_inputs - INFO - [UpdateTransformerCalc] Salvando valor de isolamento: nbi_at=325
2025-05-25 13:23:38 - callbacks.transformer_inputs - INFO - [UpdateTransformerCalc] Salvando valor de isolamento: sil_at=NA_SIL
2025-05-25 13:23:38 - callbacks.transformer_inputs - INFO - [UpdateTransformerCalc] Salvando valor de isolamento: teste_tensao_aplicada_at=140
2025-05-25 13:23:38 - callbacks.transformer_inputs - INFO - [UpdateTransformerCalc] Sincronizando elevação de enrolamento: AT=BT=Terciário=55°C
2025-05-25 13:23:38 - app_core.transformer_mcp - DEBUG - [MCP SET] Dados definidos para store 'transformer-inputs-store'.
2025-05-25 13:23:38 - app_core.transformer_mcp - INFO - [MCP SAVE_TO_DISK] Salvando estado do MCP em disco...
2025-05-25 13:23:38 - app_core.transformer_mcp - INFO - [MCP SAVE_TO_DISK] Estado do MCP salvo em disco com sucesso.
2025-05-25 13:23:38 - callbacks.transformer_inputs - DEBUG - [UpdateTransformerCalc] MCP atualizado e salvo no disco.
2025-05-25 13:23:38 - callbacks.transformer_inputs - DEBUG - [UpdateTransformerCalc] Dados propagados para todos os stores alvo.
2025-05-25 13:23:39 - callbacks.transformer_inputs - DEBUG - [UpdateTransformerCalc] Acionado por: teste_tensao_aplicada_terciario
2025-05-25 13:23:39 - callbacks.transformer_inputs - INFO - [UpdateTransformerCalc] Salvando valor de isolamento: nbi_at=325
2025-05-25 13:23:39 - callbacks.transformer_inputs - INFO - [UpdateTransformerCalc] Salvando valor de isolamento: sil_at=NA_SIL
2025-05-25 13:23:39 - callbacks.transformer_inputs - INFO - [UpdateTransformerCalc] Salvando valor de isolamento: teste_tensao_aplicada_at=140
2025-05-25 13:23:39 - callbacks.transformer_inputs - INFO - [UpdateTransformerCalc] Sincronizando elevação de enrolamento: AT=BT=Terciário=55°C
2025-05-25 13:23:39 - app_core.transformer_mcp - DEBUG - [MCP SET] Dados definidos para store 'transformer-inputs-store'.
2025-05-25 13:23:39 - app_core.transformer_mcp - INFO - [MCP SAVE_TO_DISK] Salvando estado do MCP em disco...
2025-05-25 13:23:39 - app_core.transformer_mcp - INFO - [MCP SAVE_TO_DISK] Estado do MCP salvo em disco com sucesso.
2025-05-25 13:23:39 - callbacks.transformer_inputs - DEBUG - [UpdateTransformerCalc] MCP atualizado e salvo no disco.
2025-05-25 13:23:39 - callbacks.transformer_inputs - DEBUG - [UpdateTransformerCalc] Dados propagados para todos os stores alvo.
2025-05-25 13:23:43 - callbacks.transformer_inputs - DEBUG - [UpdateTransformerCalc] Acionado por: tensao_terciario
2025-05-25 13:23:43 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO] Acionado por: tensao_terciario. Norma: IEC, Um_kv: 24, Tensão: 13.1, Path: /dados
2025-05-25 13:23:43 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO-NEUTRO] Acionado por: tensao_terciario. Norma: IEC, Um_Neutro: None, Conexão: triangulo, Tensão: 13.1
2025-05-25 13:23:43 - callbacks.transformer_inputs - INFO - [UpdateTransformerCalc] Salvando valor de isolamento: nbi_at=325
2025-05-25 13:23:43 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO] Valores do MCP: NBI=None, SIL=None, TA=None, TI=None
2025-05-25 13:23:43 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO-NEUTRO] Valores do MCP: NBI=None, SIL=None
2025-05-25 13:23:43 - callbacks.transformer_inputs - INFO - [UpdateTransformerCalc] Salvando valor de isolamento: sil_at=NA_SIL
2025-05-25 13:23:43 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO] Valores salvos: NBI=95, SIL=NA_SIL, TA=50, TI=/dados
2025-05-25 13:23:43 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO-NEUTRO] Valores salvos: NBI=, SIL=
2025-05-25 13:23:43 - callbacks.transformer_inputs - INFO - [UpdateTransformerCalc] Salvando valor de isolamento: teste_tensao_aplicada_at=140
2025-05-25 13:23:43 - callbacks.transformer_inputs - INFO - [UpdateTransformerCalc] Sincronizando elevação de enrolamento: AT=BT=Terciário=55°C
2025-05-25 13:23:43 - app_core.transformer_mcp - DEBUG - [MCP SET] Dados definidos para store 'transformer-inputs-store'.
2025-05-25 13:23:43 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - TERCIARIO] Opções FILTRADAS por Um=24.0 e Norma=IEC.
2025-05-25 13:23:43 - app_core.transformer_mcp - INFO - [MCP SAVE_TO_DISK] Salvando estado do MCP em disco...
2025-05-25 13:23:43 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - TERCIARIO] Verificando se valor salvo de NBI (95) já está nas opções: True
2025-05-25 13:23:43 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - TERCIARIO] Opções disponíveis para NBI: ['95', '125', '145']
2025-05-25 13:23:43 - app_core.transformer_mcp - INFO - [MCP SAVE_TO_DISK] Estado do MCP salvo em disco com sucesso.
2025-05-25 13:23:43 - callbacks.transformer_inputs - DEBUG - [UpdateTransformerCalc] MCP atualizado e salvo no disco.
2025-05-25 13:23:43 - callbacks.transformer_inputs - DEBUG - [UpdateTransformerCalc] Dados propagados para todos os stores alvo.
2025-05-25 13:23:43 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO] Acionado por: tensao_terciario. Norma: IEC, Um_kv: 24, Tensão: 13, Path: /dados
2025-05-25 13:23:43 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO-NEUTRO] Acionado por: tensao_terciario. Norma: IEC, Um_Neutro: None, Conexão: triangulo, Tensão: 13
2025-05-25 13:23:43 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO] Valores do MCP: NBI=None, SIL=None, TA=None, TI=None
2025-05-25 13:23:43 - callbacks.transformer_inputs - DEBUG - [UpdateTransformerCalc] Acionado por: tensao_terciario
2025-05-25 13:23:43 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO-NEUTRO] Valores do MCP: NBI=None, SIL=None
2025-05-25 13:23:43 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO] Valores salvos: NBI=95, SIL=NA_SIL, TA=50, TI=/dados
2025-05-25 13:23:43 - callbacks.transformer_inputs - INFO - [UpdateTransformerCalc] Salvando valor de isolamento: nbi_at=325
2025-05-25 13:23:43 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO-NEUTRO] Valores salvos: NBI=, SIL=
2025-05-25 13:23:43 - callbacks.transformer_inputs - INFO - [UpdateTransformerCalc] Salvando valor de isolamento: sil_at=NA_SIL
2025-05-25 13:23:43 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - TERCIARIO] Opções FILTRADAS por Um=24.0 e Norma=IEC.
2025-05-25 13:23:43 - callbacks.transformer_inputs - INFO - [UpdateTransformerCalc] Salvando valor de isolamento: teste_tensao_aplicada_at=140
2025-05-25 13:23:43 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - TERCIARIO] Verificando se valor salvo de NBI (95) já está nas opções: True
2025-05-25 13:23:43 - callbacks.transformer_inputs - INFO - [UpdateTransformerCalc] Sincronizando elevação de enrolamento: AT=BT=Terciário=55°C
2025-05-25 13:23:43 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - TERCIARIO] Opções disponíveis para NBI: ['95', '125', '145']
2025-05-25 13:23:43 - app_core.transformer_mcp - DEBUG - [MCP SET] Dados definidos para store 'transformer-inputs-store'.
2025-05-25 13:23:43 - app_core.transformer_mcp - INFO - [MCP SAVE_TO_DISK] Salvando estado do MCP em disco...
2025-05-25 13:23:43 - app_core.transformer_mcp - INFO - [MCP SAVE_TO_DISK] Estado do MCP salvo em disco com sucesso.
2025-05-25 13:23:43 - callbacks.transformer_inputs - DEBUG - [UpdateTransformerCalc] MCP atualizado e salvo no disco.
2025-05-25 13:23:43 - callbacks.transformer_inputs - DEBUG - [UpdateTransformerCalc] Dados propagados para todos os stores alvo.
2025-05-25 13:23:45 - callbacks.transformer_inputs - INFO - [LOAD TransformerInputs] Acionado por: url
2025-05-25 13:23:45 - callbacks.transformer_inputs - DEBUG - [LOAD TransformerInputs] Não estamos na página de dados (pathname=/perdas, clean_path=perdas), prevenindo atualização
2025-05-25 13:23:45 - utils.mcp_persistence - INFO - [ensure_mcp_data_propagation] Store de origem transformer-inputs-store É a fonte-de-verdade para campos básicos
2025-05-25 13:23:45 - app_core.transformer_mcp - DEBUG - [MCP SET] Dados definidos para store 'losses-store'.
2025-05-25 13:23:45 - utils.mcp_persistence - INFO - [ensure_mcp_data_propagation] Dados básicos propagados para losses-store
2025-05-25 13:23:45 - layouts.losses - INFO - Creating Losses layout...
2025-05-25 13:23:45 - layouts.losses - INFO - [Losses Layout] Dados do transformador obtidos do losses-store.
2025-05-25 13:23:45 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT] Acionado por: url. Norma: IEC, Um_kv: 69, Tensão: 69.1, Path: /perdas
2025-05-25 13:23:45 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT] Valores do MCP: NBI=None, SIL=None, TA=None, TI=None
2025-05-25 13:23:45 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT] Acionado por: url. Norma: IEC, Um_kv: 3.6, Tensão: 13.8, Path: /perdas
2025-05-25 13:23:45 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT] Valores salvos: NBI=325, SIL=NA_SIL, TA=140, TI=
2025-05-25 13:23:45 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO] Acionado por: url. Norma: IEC, Um_kv: 24, Tensão: 13, Path: /perdas
2025-05-25 13:23:45 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT] Valores do MCP: NBI=None, SIL=None, TA=None, TI=None
2025-05-25 13:23:45 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT-NEUTRO] Acionado por: url. Norma: IEC, Um_Neutro: None, Conexão: triangulo, Tensão: 69.1
2025-05-25 13:23:46 - app_core.isolation_repo - WARNING - [ISOLATION] Nenhum registro encontrado para Um=69.0kV e norma=IEC/NBR. Usando valores padrão.
2025-05-25 13:23:45 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO] Valores do MCP: NBI=None, SIL=None, TA=None, TI=None
2025-05-25 13:23:46 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT] Valores salvos: NBI=20, SIL=NA_SIL, TA=10, TI=/perdas
2025-05-25 13:23:46 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT-NEUTRO] Acionado por: url. Norma: IEC, Um_Neutro: None, Conexão: triangulo, Tensão: 13.8
2025-05-25 13:23:46 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT-NEUTRO] Valores do MCP: NBI=None, SIL=None
2025-05-25 13:23:46 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - AT] Opções FILTRADAS por Um=69.0 e Norma=IEC.
2025-05-25 13:23:46 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO-NEUTRO] Acionado por: url. Norma: IEC, Um_Neutro: None, Conexão: triangulo, Tensão: 13
2025-05-25 13:23:46 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO] Valores salvos: NBI=95, SIL=NA_SIL, TA=50, TI=/perdas
2025-05-25 13:23:46 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT-NEUTRO] Valores do MCP: NBI=None, SIL=None
2025-05-25 13:23:46 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT-NEUTRO] Valores salvos: NBI=, SIL=
2025-05-25 13:23:46 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - AT] Verificando se valor salvo de NBI (325) já está nas opções: True
2025-05-25 13:23:46 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - BT] Opções FILTRADAS por Um=3.6 e Norma=IEC.
2025-05-25 13:23:46 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO-NEUTRO] Valores do MCP: NBI=None, SIL=None
2025-05-25 13:23:46 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT-NEUTRO] Valores salvos: NBI=None, SIL=
2025-05-25 13:23:46 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - TERCIARIO] Opções FILTRADAS por Um=24.0 e Norma=IEC.
2025-05-25 13:23:46 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - AT] Opções disponíveis para NBI: ['325']
2025-05-25 13:23:46 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - BT] Verificando se valor salvo de NBI (20) já está nas opções: True
2025-05-25 13:23:46 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO-NEUTRO] Valores salvos: NBI=, SIL=
2025-05-25 13:23:46 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - TERCIARIO] Verificando se valor salvo de NBI (95) já está nas opções: True
2025-05-25 13:23:46 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - BT] Opções disponíveis para NBI: ['20', '40']
2025-05-25 13:23:46 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - TERCIARIO] Opções disponíveis para NBI: ['95', '125', '145']
2025-05-25 13:23:46 - callbacks.global_updates - WARNING - Dados essenciais ausentes no transformer_store_data
2025-05-25 13:23:46 - components.transformer_info_template - WARNING - transformer_data inválido ou vazio: {}
2025-05-25 13:23:46 - callbacks.global_updates - DEBUG - Painel atualizado em 2.0ms
2025-05-25 13:23:46 - callbacks.losses - CRITICAL - [LOSSES POPULATE CARGA] Acionado. Aba: tab-carga, Trigger: N/A
2025-05-25 13:23:47 - callbacks.losses - CRITICAL - [LOSSES POPULATE CARGA] Acionado. Aba: tab-vazio, Trigger: tabs-perdas
2025-05-25 13:23:47 - callbacks.losses - CRITICAL - [LOSSES POPULATE VAZIO] Acionado. Aba: tab-vazio, Trigger: N/A
2025-05-25 13:23:48 - callbacks.losses - CRITICAL - [LOSSES POPULATE VAZIO] Acionado. Aba: tab-carga, Trigger: tabs-perdas
2025-05-25 13:23:48 - callbacks.losses - CRITICAL - [LOSSES POPULATE CARGA] Acionado. Aba: tab-carga, Trigger: N/A
2025-05-25 13:25:08 - layouts.impulse - INFO - Criando layout de impulso...
2025-05-25 13:25:08 - layouts.impulse - INFO - [Impulse] Dados do transformador obtidos do MCP: 58
2025-05-25 13:25:08 - callbacks.global_updates - WARNING - Dados essenciais ausentes no transformer_store_data
2025-05-25 13:25:08 - components.transformer_info_template - WARNING - transformer_data inválido ou vazio: {}
2025-05-25 13:25:08 - callbacks.global_updates - DEBUG - Painel atualizado em 0.0ms
2025-05-25 13:25:09 - utils.mcp_persistence - INFO - [ensure_mcp_data_propagation] Store de origem transformer-inputs-store É a fonte-de-verdade para campos básicos
2025-05-25 13:25:09 - app_core.transformer_mcp - DEBUG - [MCP SET] Dados definidos para store 'losses-store'.
2025-05-25 13:25:09 - utils.mcp_persistence - INFO - [ensure_mcp_data_propagation] Dados básicos propagados para losses-store
2025-05-25 13:25:09 - layouts.losses - INFO - Creating Losses layout...
2025-05-25 13:25:09 - layouts.losses - INFO - [Losses Layout] Dados do transformador obtidos do losses-store.
2025-05-25 13:25:10 - callbacks.global_updates - WARNING - Dados essenciais ausentes no transformer_store_data
2025-05-25 13:25:10 - components.transformer_info_template - WARNING - transformer_data inválido ou vazio: {}
2025-05-25 13:25:10 - callbacks.global_updates - DEBUG - Painel atualizado em 2.0ms
2025-05-25 13:25:10 - callbacks.losses - CRITICAL - [LOSSES POPULATE CARGA] Acionado. Aba: tab-carga, Trigger: N/A
2025-05-25 13:25:10 - layouts.transformer_inputs - INFO - Criando layout Dados Básicos (v5 - Layout Geral e Pesos em 2 linhas)...
2025-05-25 13:25:10 - layouts.transformer_inputs - INFO - Classes de tensão carregadas para dropdown: 27 opções.
2025-05-25 13:25:10 - callbacks.global_updates - WARNING - Dados essenciais ausentes no transformer_store_data
2025-05-25 13:25:10 - components.transformer_info_template - WARNING - transformer_data inválido ou vazio: {}
2025-05-25 13:25:10 - callbacks.transformer_inputs - INFO - [LOAD TransformerInputs] Acionado por: None
2025-05-25 13:25:10 - callbacks.global_updates - DEBUG - Painel atualizado em 1.0ms
2025-05-25 13:25:10 - callbacks.transformer_inputs - INFO - [LOAD TransformerInputs] Opções dos dropdowns ainda não foram populadas, aguardando...
2025-05-25 13:25:10 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_kv: 69, Tensão: 69.1, Path: /dados
2025-05-25 13:25:10 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_kv: 3.6, Tensão: 13.8, Path: /dados
2025-05-25 13:25:10 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT-NEUTRO] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_Neutro: None, Conexão: triangulo, Tensão: 13.8
2025-05-25 13:25:10 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT-NEUTRO] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_Neutro: None, Conexão: triangulo, Tensão: 69.1
2025-05-25 13:25:10 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT] Valores do MCP: NBI=None, SIL=None, TA=None, TI=None
2025-05-25 13:25:10 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT] Valores do MCP: NBI=None, SIL=None, TA=None, TI=None
2025-05-25 13:25:10 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_kv: 24, Tensão: 13, Path: /dados
2025-05-25 13:25:10 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO-NEUTRO] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_Neutro: None, Conexão: triangulo, Tensão: 13
2025-05-25 13:25:10 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT-NEUTRO] Valores do MCP: NBI=None, SIL=None
2025-05-25 13:25:10 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT-NEUTRO] Valores do MCP: NBI=None, SIL=None
2025-05-25 13:25:10 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT] Valores salvos: NBI=None, SIL=None, TA=None, TI=
2025-05-25 13:25:10 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT] Valores salvos: NBI=None, SIL=None, TA=None, TI=/dados
2025-05-25 13:25:10 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO] Valores do MCP: NBI=None, SIL=None, TA=None, TI=None
2025-05-25 13:25:10 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO-NEUTRO] Valores do MCP: NBI=None, SIL=None
2025-05-25 13:25:10 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT-NEUTRO] Valores salvos: NBI=None, SIL=
2025-05-25 13:25:10 - app_core.isolation_repo - WARNING - [ISOLATION] Nenhum registro encontrado para Um=69.0kV e norma=IEC/NBR. Usando valores padrão.
2025-05-25 13:25:10 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT-NEUTRO] Valores salvos: NBI=, SIL=
2025-05-25 13:25:10 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO] Valores salvos: NBI=None, SIL=None, TA=None, TI=/dados
2025-05-25 13:25:10 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - BT] Opções FILTRADAS por Um=3.6 e Norma=IEC.
2025-05-25 13:25:10 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO-NEUTRO] Valores salvos: NBI=, SIL=
2025-05-25 13:25:10 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - AT] Opções FILTRADAS por Um=69.0 e Norma=IEC.
2025-05-25 13:25:10 - callbacks.transformer_inputs - DEBUG - [UpdateTransformerCalc] Acionado por: nbi_at
2025-05-25 13:25:10 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - TERCIARIO] Opções FILTRADAS por Um=24.0 e Norma=IEC.
2025-05-25 13:25:10 - callbacks.transformer_inputs - INFO - [UpdateTransformerCalc] Sincronizando elevação de enrolamento: AT=BT=Terciário=55°C
2025-05-25 13:25:10 - app_core.transformer_mcp - DEBUG - [MCP SET] Dados definidos para store 'transformer-inputs-store'.
2025-05-25 13:25:10 - app_core.transformer_mcp - INFO - [MCP SAVE_TO_DISK] Salvando estado do MCP em disco...
2025-05-25 13:25:10 - app_core.transformer_mcp - INFO - [MCP SAVE_TO_DISK] Estado do MCP salvo em disco com sucesso.
2025-05-25 13:25:10 - callbacks.transformer_inputs - DEBUG - [UpdateTransformerCalc] MCP atualizado e salvo no disco.
2025-05-25 13:25:10 - callbacks.transformer_inputs - DEBUG - [UpdateTransformerCalc] Dados propagados para todos os stores alvo.
