2025-05-25 13:39:14 - app - ERROR - Erro ao importar módulo para registro explícito applied_voltage: cannot import name 'COLORS' from 'layouts' (C:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\layouts\__init__.py)
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\app.py", line 224, in <module>
    module = __import__(module_path, fromlist=[reg_func_name])
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\callbacks\applied_voltage.py", line 27, in <module>
    from layouts import COLORS  # Para estilos padronizados
    ^^^^^^^^^^^^^^^^^^^^^^^^^^
ImportError: cannot import name 'COLORS' from 'layouts' (C:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\layouts\__init__.py)
2025-05-25 13:39:14 - app - ERROR - Erro ao importar módulo para registro explícito history: cannot import name 'COLORS' from 'layouts' (C:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\layouts\__init__.py)
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\app.py", line 224, in <module>
    module = __import__(module_path, fromlist=[reg_func_name])
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\callbacks\history.py", line 9, in <module>
    from layouts import COLORS # Para estilos de botões na tabela, se necessário
    ^^^^^^^^^^^^^^^^^^^^^^^^^^
ImportError: cannot import name 'COLORS' from 'layouts' (C:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\layouts\__init__.py)
2025-05-25 13:39:14 - callbacks.temperature_rise - INFO - Registrando callbacks do módulo temperature_rise para app Simulador de Testes de Transformadores (IEC/IEEE/ABNT) EPS 1500...
2025-05-25 13:39:14 - callbacks.temperature_rise - INFO - Callbacks do módulo temperature_rise já registrados via decoradores @app.callback.
2025-05-25 13:39:14 - app - INFO - Callbacks registrados. Total: 40
2025-05-25 13:39:14 - __main__ - ERROR - Erro ao importar módulo de callback navigation_dcc_links: cannot import name 'COLORS' from 'layouts' (C:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\layouts\__init__.py)
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\app.py", line 201, in <module>
    __import__(module_path)
  File "C:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\callbacks\navigation_dcc_links.py", line 11, in <module>
    from layouts import COLORS
ImportError: cannot import name 'COLORS' from 'layouts' (C:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\layouts\__init__.py)
2025-05-25 13:39:14 - __main__ - ERROR - Erro inesperado ao processar módulo de callback losses: 'dropdown'
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\app.py", line 201, in <module>
    __import__(module_path)
  File "C:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\callbacks\losses.py", line 216, in <module>
    from layouts.losses import render_perdas_carga, render_perdas_vazio
  File "C:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\layouts\losses.py", line 60, in <module>
    **COMPONENTS["dropdown"],
      ~~~~~~~~~~^^^^^^^^^^^^
KeyError: 'dropdown'
2025-05-25 13:39:14 - __main__ - ERROR - Erro ao importar módulo de callback dielectric_analysis_comprehensive: cannot import name 'COLORS' from 'layouts' (C:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\layouts\__init__.py)
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\app.py", line 201, in <module>
    __import__(module_path)
  File "C:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\callbacks\dielectric_analysis_comprehensive.py", line 16, in <module>
    from layouts import COLORS
ImportError: cannot import name 'COLORS' from 'layouts' (C:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\layouts\__init__.py)
2025-05-25 13:39:14 - callbacks.insulation_level_callbacks - INFO - Registrando callbacks de níveis de isolamento (APENAS OPÇÕES - V2)...
2025-05-25 13:39:14 - callbacks.insulation_level_callbacks - INFO - Callbacks de níveis de isolamento (APENAS OPÇÕES - V2) registrados.
2025-05-25 13:39:14 - callbacks.transformer_inputs - INFO - Registrando callbacks do módulo transformer_inputs para app Simulador de Testes de Transformadores (IEC/IEEE/ABNT) EPS 1500...
2025-05-25 13:39:14 - __main__ - ERROR - Erro ao importar módulo para registro explícito applied_voltage: cannot import name 'COLORS' from 'layouts' (C:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\layouts\__init__.py)
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\app.py", line 224, in <module>
    module = __import__(module_path, fromlist=[reg_func_name])
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\callbacks\applied_voltage.py", line 27, in <module>
    from layouts import COLORS  # Para estilos padronizados
    ^^^^^^^^^^^^^^^^^^^^^^^^^^
ImportError: cannot import name 'COLORS' from 'layouts' (C:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\layouts\__init__.py)
2025-05-25 13:39:14 - __main__ - ERROR - Erro ao importar módulo para registro explícito history: cannot import name 'COLORS' from 'layouts' (C:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\layouts\__init__.py)
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\app.py", line 224, in <module>
    module = __import__(module_path, fromlist=[reg_func_name])
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\callbacks\history.py", line 9, in <module>
    from layouts import COLORS # Para estilos de botões na tabela, se necessário
    ^^^^^^^^^^^^^^^^^^^^^^^^^^
ImportError: cannot import name 'COLORS' from 'layouts' (C:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\layouts\__init__.py)
2025-05-25 13:39:14 - callbacks.temperature_rise - INFO - Registrando callbacks do módulo temperature_rise para app Simulador de Testes de Transformadores (IEC/IEEE/ABNT) EPS 1500...
2025-05-25 13:39:14 - callbacks.temperature_rise - INFO - Callbacks do módulo temperature_rise já registrados via decoradores @app.callback.
2025-05-25 13:39:14 - __main__ - INFO - Callbacks registrados. Total: 25
2025-05-25 13:39:14 - __main__ - INFO - Iniciando servidor Dash em http://127.0.0.1:8050/ (Debug: True)
2025-05-25 13:39:14 - dash.dash - INFO - Dash is running on http://127.0.0.1:8050/

2025-05-25 13:39:34 - callbacks.global_updates - DEBUG - Painel atualizado em 3.0ms
2025-05-25 13:39:35 - callbacks.global_updates - DEBUG - Painel atualizado em 2.0ms
2025-05-25 13:39:39 - callbacks.global_updates - DEBUG - Painel atualizado em 2.0ms
2025-05-25 13:40:26 - app - ERROR - Erro ao importar módulo para registro explícito temperature_rise: No module named 'utils.styles'
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\app.py", line 224, in <module>
    module = __import__(module_path, fromlist=[reg_func_name])
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\callbacks\temperature_rise.py", line 11, in <module>
    from config import colors
  File "C:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\config.py", line 67, in <module>
    from utils.styles import COLORS, COMPONENTS, MESSAGE_STYLES, SPACING, TABLE_STYLES, TYPOGRAPHY
ModuleNotFoundError: No module named 'utils.styles'
2025-05-25 13:40:26 - app - INFO - Callbacks registrados. Total: 32
2025-05-25 13:40:26 - layouts.transformer_inputs - ERROR - Falha ao importar estilos: No module named 'utils.styles'. Usando fallbacks.
2025-05-25 13:40:26 - __main__ - ERROR - Erro inesperado ao processar módulo de callback navigation_dcc_links: 'background_card_header'
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\app.py", line 201, in <module>
    __import__(module_path)
  File "C:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\callbacks\navigation_dcc_links.py", line 33, in <module>
    from layouts.losses import create_losses_layout  # Agora em losses.py
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\layouts\losses.py", line 72, in <module>
    "backgroundColor": COLORS["background_card_header"],
                       ~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^
KeyError: 'background_card_header'
2025-05-25 13:40:26 - __main__ - ERROR - Erro ao importar módulo de callback losses: No module named 'utils.styles'
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\app.py", line 201, in <module>
    __import__(module_path)
  File "C:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\callbacks\losses.py", line 29, in <module>
    from config import colors as CONFIG_COLORS
  File "C:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\config.py", line 67, in <module>
    from utils.styles import COLORS, COMPONENTS, MESSAGE_STYLES, SPACING, TABLE_STYLES, TYPOGRAPHY
ModuleNotFoundError: No module named 'utils.styles'
2025-05-25 13:40:26 - __main__ - ERROR - Erro ao importar módulo de callback dieletric_analysis: No module named 'utils.styles'
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\app.py", line 201, in <module>
    __import__(module_path)
  File "C:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\callbacks\dieletric_analysis.py", line 12, in <module>
    import config
  File "C:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\config.py", line 67, in <module>
    from utils.styles import COLORS, COMPONENTS, MESSAGE_STYLES, SPACING, TABLE_STYLES, TYPOGRAPHY
ModuleNotFoundError: No module named 'utils.styles'
2025-05-25 13:40:26 - __main__ - ERROR - Erro ao importar módulo de callback report_generation: No module named 'utils.styles'
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\app.py", line 201, in <module>
    __import__(module_path)
  File "C:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\callbacks\report_generation.py", line 24, in <module>
    from utils.pdf_generator import generate_pdf
  File "C:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\utils\pdf_generator.py", line 24, in <module>
    import config
  File "C:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\config.py", line 67, in <module>
    from utils.styles import COLORS, COMPONENTS, MESSAGE_STYLES, SPACING, TABLE_STYLES, TYPOGRAPHY
ModuleNotFoundError: No module named 'utils.styles'
2025-05-25 13:40:26 - __main__ - ERROR - Erro ao importar módulo de callback dielectric_analysis_comprehensive: No module named 'utils.styles'
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\app.py", line 201, in <module>
    __import__(module_path)
  File "C:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\callbacks\dielectric_analysis_comprehensive.py", line 12, in <module>
    from components.ui_elements import create_comparison_table
  File "C:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\components\ui_elements.py", line 12, in <module>
    import config
  File "C:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\config.py", line 67, in <module>
    from utils.styles import COLORS, COMPONENTS, MESSAGE_STYLES, SPACING, TABLE_STYLES, TYPOGRAPHY
ModuleNotFoundError: No module named 'utils.styles'
2025-05-25 13:40:26 - callbacks.insulation_level_callbacks - INFO - Registrando callbacks de níveis de isolamento (APENAS OPÇÕES - V2)...
2025-05-25 13:40:26 - callbacks.insulation_level_callbacks - INFO - Callbacks de níveis de isolamento (APENAS OPÇÕES - V2) registrados.
2025-05-25 13:40:26 - callbacks.transformer_inputs - INFO - Registrando callbacks do módulo transformer_inputs para app Simulador (Erro Config)...
2025-05-25 13:40:26 - __main__ - ERROR - Erro ao importar módulo para registro explícito short_circuit: No module named 'utils.styles'
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\app.py", line 224, in <module>
    module = __import__(module_path, fromlist=[reg_func_name])
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\callbacks\short_circuit.py", line 15, in <module>
    from config import colors  # Importar cores para estilos de status
    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\config.py", line 67, in <module>
    from utils.styles import COLORS, COMPONENTS, MESSAGE_STYLES, SPACING, TABLE_STYLES, TYPOGRAPHY
ModuleNotFoundError: No module named 'utils.styles'
2025-05-25 13:40:26 - __main__ - ERROR - Erro ao importar módulo para registro explícito temperature_rise: No module named 'utils.styles'
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\app.py", line 224, in <module>
    module = __import__(module_path, fromlist=[reg_func_name])
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\callbacks\temperature_rise.py", line 11, in <module>
    from config import colors
  File "C:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\config.py", line 67, in <module>
    from utils.styles import COLORS, COMPONENTS, MESSAGE_STYLES, SPACING, TABLE_STYLES, TYPOGRAPHY
ModuleNotFoundError: No module named 'utils.styles'
2025-05-25 13:40:26 - __main__ - INFO - Callbacks registrados. Total: 32
2025-05-25 13:40:26 - __main__ - INFO - Iniciando servidor Dash em http://127.0.0.1:8060/ (Debug: False)
2025-05-25 13:40:26 - __main__ - INFO - Timer para abertura do navegador iniciado
2025-05-25 13:40:26 - dash.dash - INFO - Dash is running on http://127.0.0.1:8060/

2025-05-25 13:40:27 - __main__ - INFO - Abrindo navegador na URL: http://127.0.0.1:8060/
2025-05-25 13:40:27 - callbacks.global_updates - DEBUG - Painel atualizado em 2.0ms
2025-05-25 13:41:46 - callbacks.temperature_rise - INFO - Registrando callbacks do módulo temperature_rise para app Simulador de Testes de Transformadores (IEC/IEEE/ABNT) EPS 1500...
2025-05-25 13:41:46 - callbacks.temperature_rise - INFO - Callbacks do módulo temperature_rise já registrados via decoradores @app.callback.
2025-05-25 13:41:46 - app - INFO - Callbacks registrados. Total: 50
2025-05-25 13:41:46 - layouts.transformer_inputs - ERROR - Falha ao importar estilos: No module named 'utils.styles'. Usando fallbacks.
2025-05-25 13:41:46 - __main__ - ERROR - Erro inesperado ao processar módulo de callback navigation_dcc_links: 'text_muted'
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\app.py", line 201, in <module>
    __import__(module_path)
  File "C:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\callbacks\navigation_dcc_links.py", line 33, in <module>
    from layouts.losses import create_losses_layout  # Agora em losses.py
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\layouts\losses.py", line 74, in <module>
    "color": COLORS["text_muted"],
             ~~~~~~^^^^^^^^^^^^^^
KeyError: 'text_muted'
2025-05-25 13:41:46 - __main__ - ERROR - Erro inesperado ao processar módulo de callback losses: 'text_muted'
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\app.py", line 201, in <module>
    __import__(module_path)
  File "C:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\callbacks\losses.py", line 216, in <module>
    from layouts.losses import render_perdas_carga, render_perdas_vazio
  File "C:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\layouts\losses.py", line 74, in <module>
    "color": COLORS["text_muted"],
             ~~~~~~^^^^^^^^^^^^^^
KeyError: 'text_muted'
2025-05-25 13:41:46 - callbacks.insulation_level_callbacks - INFO - Registrando callbacks de níveis de isolamento (APENAS OPÇÕES - V2)...
2025-05-25 13:41:46 - callbacks.insulation_level_callbacks - INFO - Callbacks de níveis de isolamento (APENAS OPÇÕES - V2) registrados.
2025-05-25 13:41:46 - callbacks.transformer_inputs - INFO - Registrando callbacks do módulo transformer_inputs para app Simulador de Testes de Transformadores (IEC/IEEE/ABNT) EPS 1500...
2025-05-25 13:41:46 - callbacks.temperature_rise - INFO - Registrando callbacks do módulo temperature_rise para app Simulador de Testes de Transformadores (IEC/IEEE/ABNT) EPS 1500...
2025-05-25 13:41:46 - callbacks.temperature_rise - INFO - Callbacks do módulo temperature_rise já registrados via decoradores @app.callback.
2025-05-25 13:41:46 - __main__ - INFO - Callbacks registrados. Total: 35
2025-05-25 13:41:46 - __main__ - INFO - Iniciando servidor Dash em http://127.0.0.1:8050/ (Debug: True)
2025-05-25 13:41:46 - dash.dash - INFO - Dash is running on http://127.0.0.1:8050/

