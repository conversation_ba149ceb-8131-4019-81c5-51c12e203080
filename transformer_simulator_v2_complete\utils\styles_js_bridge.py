# utils/styles_js_bridge.py
"""
Ponte para obter estilos JavaScript via Dash Clientside Callbacks.
Este módulo permite integração entre JavaScript e Python para estilos.
"""
import json
import logging

import dash
from dash import Output, Input, html, dcc, clientside_callback, ClientsideFunction
from dash.exceptions import PreventUpdate

log = logging.getLogger(__name__)


def register_style_clientside_callbacks(app):
    """
    Registra os callbacks clientside para integração dos estilos JS com Python.
    """
    try:
        # Callback que obtém estilos JavaScript e os disponibiliza para Python
        clientside_callback(
            ClientsideFunction('styles_integration', 'getStyles'),
            Output('styles-store', 'data'),
            Input('styles-store-trigger', 'n_intervals'),
        )
        log.info("Callbacks clientside de estilos registrados com sucesso")
    except Exception as e:
        log.error(f"Erro ao registrar callbacks clientside de estilos: {e}")


def create_style_stores():
    """
    Cria os componentes de store necessários para a comunicação de estilos JS <-> Python.
    """
    return html.Div(
        [
            dcc.Store(id='styles-store', storage_type='memory'),
            dcc.Interval(id='styles-store-trigger', interval=1000, max_intervals=1),
        ],
        style={'display': 'none'}
    )


def set_theme(app, theme_name):
    """
    Aciona o callback clientside para alternar entre temas claro e escuro.
    """
    try:
        # Esta função deve ser chamada dentro de um callback Dash
        return dash.ctx.app.clientside_callback(
            ClientsideFunction('styles_integration', 'setTheme'),
            Output('theme-status', 'data'),
            Input('theme-name', 'data'),
        )
    except Exception as e:
        log.error(f"Erro ao alternar tema via clientside: {e}")
        return None
