"""
styles_bridge.py
Ponte entre os estilos JavaScript na pasta assets e os módulos Python.
Este módulo importa os estilos necessários dos arquivos JavaScript para uso nos módulos Python.
"""
import json
import logging
from pathlib import Path

# Configurar logging
log = logging.getLogger(__name__)

# Caminho para os arquivos de estilo JavaScript
ASSETS_DIR = Path(__file__).parent.parent / "assets"
THEME_COLORS_JS_PATH = ASSETS_DIR / "theme_colors.js"
STYLES_JS_PATH = ASSETS_DIR / "styles.js"

# Converter constantes JavaScript para dicionários Python
def extract_js_object(file_path, obj_name):
    """
    Extrai um objeto JavaScript para um dicionário Python.
    Devido à complexidade dos arquivos JS, essa função agora usa uma abordagem simplificada 
    e robusta para extrair apenas definições de objetos literais simples.
    """
    try:
        with open(file_path, "r", encoding="utf-8") as f:
            content = f.read()
        
        # Em vez de tentar analisar o JavaScript completo, vamos apenas criar um 
        # dicionário Python manualmente com base em padrões comuns

        # Verificar se é definido como const ou diretamente em window
        start_markers = [
            f"const {obj_name} = {{",  # const COLORS = {
            f"window.{obj_name} = {{",  # window.COLORS = {
            f"let {obj_name} = {{",     # let COLORS = {
            f"var {obj_name} = {{",     # var COLORS = {
            f"{obj_name} = {{"          # COLORS = {
        ]
        
        # Tentar cada marcador possível
        found_start = False
        for marker in start_markers:
            start_idx = content.find(marker)
            if start_idx != -1:
                start_idx += len(marker) - 1  # -1 para incluir o {
                found_start = True
                break
                
        if not found_start:
            log.warning(f"Definição de {obj_name} não encontrada em {file_path}")
            return {}
            
        # Contar chaves para encontrar o final do objeto
        obj_content = content[start_idx:]
        level = 0
        end_idx = 0
        in_string = False
        escape_next = False
        
        for i, char in enumerate(obj_content):
            if escape_next:
                escape_next = False
                continue
                
            if char == '\\':
                escape_next = True
                continue
                
            if char == '"' or char == "'":
                in_string = not in_string
                continue
                
            if not in_string:
                if char == '{':
                    level += 1
                elif char == '}':
                    level -= 1
                    if level == 0:
                        end_idx = i + 1
                        break
        
        if end_idx == 0:
            log.warning(f"Não foi possível encontrar o fim do objeto {obj_name}")
            return {}
            
        # Extrair o objeto JavaScript
        js_object = obj_content[:end_idx]
        
        # Parsing manual, já que a conversão para JSON é problemática
        # Este é um approach simplificado que irá funcionar para objetos simples
        result = {}
        
        # Extrair cada propriedade de nível superior
        import re
        # Padrão para propriedades do tipo "chave": valor ou chave: valor
        prop_pattern = re.compile(r'(?:[\'"]([\w_]+)[\'"]|(\w+))\s*:\s*([^,}]+)')
        
        for match in prop_pattern.finditer(js_object):
            key = match.group(1) or match.group(2)  # Captura chave com ou sem aspas
            value_str = match.group(3).strip()
            
            # Processar o valor baseado no tipo
            if value_str.startswith('"') or value_str.startswith("'"):
                # É uma string - remover aspas
                value = value_str.strip('\'"')
            elif value_str.startswith('#'):
                # É uma cor hex
                value = value_str
            elif value_str.lower() in ['true', 'false']:
                # É um booleano
                value = value_str.lower() == 'true'
            elif value_str.replace('.', '', 1).isdigit():
                # É um número
                value = float(value_str) if '.' in value_str else int(value_str)
            elif value_str.startswith('COLORS['):
                # Referência a COLORS["something"]
                colors_key = re.search(r'\["([^"]+)"\]', value_str)
                if colors_key and COLORS.get(colors_key.group(1)):
                    value = COLORS[colors_key.group(1)]
                else:
                    value = "#000000"  # Valor padrão se a cor não for encontrada
            else:
                # Outro valor não processado
                value = value_str
                
            result[key] = value
            
        return result
        
    except Exception as e:
        log.error(f"Erro ao extrair objeto {obj_name} de {file_path}: {e}")
        return {}

# -- FALLBACK VALUES -- #
# Estes são valores de backup caso a extração falhe
DEFAULT_COLORS = {
    "primary": "#26427A",
    "secondary": "#6c757d",
    "accent": "#00BFFF",
    "accent_alt": "#FFD700",
    "background_main": "#1a1a1a",
    "background_card": "#2c2c2c",
    "background_card_header": "#1f1f1f",
    "background_input": "#3a3a3a",
    "background_header": "#1f1f1f",
    "background_faint": "#333333",
    "text_light": "#e0e0e0",
    "text_dark": "#e0e0e0",
    "text_muted": "#a0a0a0",
    "text_header": "#FFFFFF",
    "border": "#444444",
    "border_light": "#555555",
    "border_strong": "#666666",
    "success": "#28a745",
    "danger": "#dc3545",
    "warning": "#ffc107",
    "info": "#00BFFF",
    "pass": "#28a745",
    "fail": "#dc3545",
    "pass_bg": "rgba(40, 167, 69, 0.2)",
    "fail_bg": "rgba(220, 53, 69, 0.2)",
    "warning_bg": "rgba(255, 193, 7, 0.2)",
}

DEFAULT_TYPOGRAPHY = {
    "title": {"fontSize": "1.1rem", "fontWeight": "bold"},
    "card_header": {"fontSize": "0.9rem", "fontWeight": "bold"},
    "section_title": {"fontSize": "0.8rem", "fontWeight": "bold"},
    "label": {"fontSize": "0.75rem", "fontWeight": "500"},
    "input": {"fontSize": "0.75rem"},
    "button": {"fontSize": "0.75rem", "fontWeight": "500"},
    "small_text": {"fontSize": "0.7rem"},
    "error_text": {"fontSize": "0.7rem", "fontWeight": "bold"},
}

DEFAULT_COMPONENTS = {
    "card": {"backgroundColor": "#2c2c2c", "border": "1px solid #444444"},
    "card_header": {"backgroundColor": "#1f1f1f", "color": "#FFFFFF"},
    "card_body": {"padding": "0.75rem", "backgroundColor": "#2c2c2c"},
    "input": {"backgroundColor": "#3a3a3a", "color": "#e0e0e0"},
    "dropdown": {"height": "28px", "fontSize": "0.75rem"},
    "read_only": {"backgroundColor": "#1f1f1f", "color": "#a0a0a0"},
    "button_base": {"padding": "0.3rem 0.6rem", "fontSize": "0.75rem"},
    "button_primary": {"backgroundColor": "#26427A", "color": "#FFFFFF"},
    "button_secondary": {"backgroundColor": "#6c757d", "color": "#FFFFFF"},
    "button_success": {"backgroundColor": "#28a745", "color": "#FFFFFF"},
    "button_danger": {"backgroundColor": "#dc3545", "color": "#FFFFFF"},
    "button_warning": {"backgroundColor": "#ffc107", "color": "#212529"},
    "button_info": {"backgroundColor": "#00BFFF", "color": "#FFFFFF"},
    "container": {"backgroundColor": "#1a1a1a", "color": "#e0e0e0", "padding": "0.5rem"},
}

DEFAULT_SPACING = {
    "row_margin": "mb-2",
    "row_gutter": "g-2",
    "col_padding": "px-1",
    "section_margin": "mb-3",
    "p": "0.5rem",
    "m": "0.5rem",
}

DEFAULT_TABLE_STYLES = {
    "header_sm": {"backgroundColor": "#1f1f1f", "color": "#FFFFFF", "fontSize": "0.7rem"},
    "header_md": {"backgroundColor": "#1f1f1f", "color": "#FFFFFF", "fontSize": "0.75rem"},
    "param_sm": {"fontSize": "0.7rem", "backgroundColor": "#1f1f1f"},
    "param_md": {"fontSize": "0.75rem", "backgroundColor": "#1f1f1f"},
    "value_sm": {"fontSize": "0.7rem", "backgroundColor": "#2c2c2c"},
    "value_md": {"fontSize": "0.75rem", "backgroundColor": "#2c2c2c"},
    "status": {"fontSize": "0.7rem", "textAlign": "center"},
    "wrapper": {"overflowX": "auto"},
}

DEFAULT_MESSAGE_STYLES = {
    "placeholder": {"fontSize": "0.8rem", "color": "#a0a0a0"},
    "error": {"fontSize": "0.75rem", "color": "#dc3545"},
}

# Extrair objetos de estilo
try:
    # Extrair cores primeiro
    DARK_COLORS = extract_js_object(THEME_COLORS_JS_PATH, "DARK_COLORS")
    LIGHT_COLORS = extract_js_object(THEME_COLORS_JS_PATH, "LIGHT_COLORS")
    
    # Verificar se as cores foram extraídas corretamente
    if not DARK_COLORS:
        log.warning("DARK_COLORS vazio, usando valores padrão")
        DARK_COLORS = DEFAULT_COLORS.copy()
    
    if not LIGHT_COLORS:
        log.warning("LIGHT_COLORS vazio, usando valores padrão")
        LIGHT_COLORS = DEFAULT_COLORS.copy()
        # Ajustar algumas cores para o tema claro
        LIGHT_COLORS.update({
            "background_main": "#f0f2f5",
            "background_card": "#ffffff",
            "text_light": "#f8f9fa",
            "text_dark": "#212529",
        })
    
    # Definir COLORS como DARK_COLORS por padrão (pode ser substituído durante a execução)
    COLORS = DARK_COLORS.copy()
    
    # Agora temos COLORS definido e podemos extrair os outros objetos que podem referenciar COLORS
    TYPOGRAPHY = extract_js_object(STYLES_JS_PATH, "TYPOGRAPHY")
    if not TYPOGRAPHY:
        log.warning("TYPOGRAPHY vazio, usando valores padrão")
        TYPOGRAPHY = DEFAULT_TYPOGRAPHY.copy()
    
    COMPONENTS = extract_js_object(STYLES_JS_PATH, "COMPONENTS")
    if not COMPONENTS:
        log.warning("COMPONENTS vazio, usando valores padrão")
        COMPONENTS = DEFAULT_COMPONENTS.copy()
    
    SPACING = extract_js_object(STYLES_JS_PATH, "SPACING")
    if not SPACING:
        log.warning("SPACING vazio, usando valores padrão")
        SPACING = DEFAULT_SPACING.copy()
    
    TABLE_STYLES = extract_js_object(STYLES_JS_PATH, "TABLE_STYLES")
    if not TABLE_STYLES:
        log.warning("TABLE_STYLES vazio, usando valores padrão")
        TABLE_STYLES = DEFAULT_TABLE_STYLES.copy()
    
    MESSAGE_STYLES = extract_js_object(STYLES_JS_PATH, "MESSAGE_STYLES")
    if not MESSAGE_STYLES:
        log.warning("MESSAGE_STYLES vazio, usando valores padrão")
        MESSAGE_STYLES = DEFAULT_MESSAGE_STYLES.copy()
    
    log.info("Estilos JavaScript extraídos com sucesso")
    
except Exception as e:
    log.error(f"Erro ao extrair estilos JavaScript: {e}")
    
    # Usar os valores padrão completos
    COLORS = DEFAULT_COLORS.copy()
    DARK_COLORS = DEFAULT_COLORS.copy()
    LIGHT_COLORS = DEFAULT_COLORS.copy()
    LIGHT_COLORS.update({
        "background_main": "#f0f2f5",
        "background_card": "#ffffff",
        "text_light": "#f8f9fa",
        "text_dark": "#212529",
    })
    TYPOGRAPHY = DEFAULT_TYPOGRAPHY.copy()
    COMPONENTS = DEFAULT_COMPONENTS.copy()
    SPACING = DEFAULT_SPACING.copy()
    TABLE_STYLES = DEFAULT_TABLE_STYLES.copy()
    MESSAGE_STYLES = DEFAULT_MESSAGE_STYLES.copy()
        "accent": "#00BFFF",
        "background_main": "#1a1a1a",
        "background_card": "#2c2c2c",
        "background_card_header": "#1f1f1f",
        "text_light": "#e0e0e0",
        "text_header": "#FFFFFF",
        "border": "#444444",
        "success": "#28a745",
        "danger": "#dc3545",
        "warning": "#ffc107",
        "info": "#00BFFF",
    }
    
    DARK_COLORS = COLORS.copy()
    LIGHT_COLORS = {
        "primary": "#26427A",
        "secondary": "#6c757d",
        "accent": "#007BFF",
        "background_main": "#f0f2f5",
        "background_card": "#ffffff",
        "background_card_header": "#e9ecef",
        "text_light": "#f8f9fa",
        "text_dark": "#212529",
        "text_header": "#FFFFFF",
        "border": "#dee2e6",
    }
    
    TYPOGRAPHY = {
        "title": {"fontSize": "1.1rem", "fontWeight": "bold"},
        "card_header": {"fontSize": "0.9rem", "fontWeight": "bold"},
        "section_title": {"fontSize": "0.8rem", "fontWeight": "bold"},
        "label": {"fontSize": "0.75rem", "fontWeight": "500"},
        "input": {"fontSize": "0.75rem"},
        "button": {"fontSize": "0.75rem", "fontWeight": "500"},
        "small_text": {"fontSize": "0.7rem"},
        "error_text": {"fontSize": "0.7rem", "fontWeight": "bold"},
    }
    
    COMPONENTS = {
        "card": {"backgroundColor": COLORS["background_card"], "border": f"1px solid {COLORS['border']}"},
        "card_header": {"backgroundColor": COLORS["background_card_header"], "color": COLORS["text_header"]},
        "card_body": {"padding": "0.75rem", "backgroundColor": COLORS["background_card"]},
    }
    
    SPACING = {"p": "0.5rem", "m": "0.5rem"}
    TABLE_STYLES = {
        "header_sm": {},
        "header_md": {},
        "param_sm": {},
        "param_md": {},
        "value_sm": {},
        "value_md": {},
        "status": {},
        "wrapper": {},
    }
    MESSAGE_STYLES = {"placeholder": {}, "error": {}}

def set_theme(theme_name):
    """Define o tema atual (claro ou escuro)."""
    global COLORS
    try:
        if theme_name.lower() == "light":
            COLORS.clear()
            COLORS.update(LIGHT_COLORS)
            log.info("Tema alterado para claro (light)")
        else:
            COLORS.clear()
            COLORS.update(DARK_COLORS)
            log.info("Tema alterado para escuro (dark)")
        
        # Garantir que todos os valores necessários estejam definidos
        for key, value in DEFAULT_COLORS.items():
            if key not in COLORS:
                COLORS[key] = value
                log.debug(f"Adicionada cor padrão ausente: {key}={value}")
                
        return COLORS
    except Exception as e:
        log.error(f"Erro ao definir tema {theme_name}: {e}")
        COLORS.clear()
        COLORS.update(DARK_COLORS if theme_name.lower() != "light" else LIGHT_COLORS)
        return COLORS
