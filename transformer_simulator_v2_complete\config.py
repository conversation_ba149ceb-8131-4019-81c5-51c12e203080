# config.py
"""
Configurações centralizadas da aplicação.
Este arquivo contém configurações essenciais para a inicialização e funcionamento da aplicação.
"""
import logging
import os
from pathlib import Path

import dash_bootstrap_components as dbc

# -----------------------------------------------------------------------------
# <PERSON><PERSON><PERSON> de Arquivos
# -----------------------------------------------------------------------------
try:
    BASE_DIR = Path(os.path.dirname(os.path.abspath(__file__)))
except NameError:
    BASE_DIR = Path(os.getcwd())

# Diretórios principais
ASSETS_DIR = BASE_DIR / "assets"
TABLES_DIR = ASSETS_DIR / "tables"
LOG_DIR = BASE_DIR / "logs"
os.makedirs(LOG_DIR, exist_ok=True)

# Arquivos de dados
PATH_NBR_DATA = TABLES_DIR / "tabelas__ABNT_NBR_5356_3.xlsx"
PATH_IEEE_DATA = TABLES_DIR / "IEEE Std C57.12.00-2000_v1.xlsx"
PATH_IEC_60060_1_DATA = TABLES_DIR / "IEC 60060_1.xlsx"
USAGE_COUNT_FILE = BASE_DIR / "usage_count.txt"
LOG_FILE = LOG_DIR / "app.log"

# -----------------------------------------------------------------------------
# Configurações da Aplicação
# -----------------------------------------------------------------------------
APP_TITLE = "Simulador de Testes de Transformadores (IEC/IEEE/ABNT) EPS 1500"
DEBUG_MODE = True  # Mantenha True para desenvolvimento
HOST = "127.0.0.1"
PORT = 8050
USAGE_LIMIT = 1000

# Configurações de tema
DEFAULT_THEME_NAME = "DARKLY"
DEFAULT_THEME_URL = getattr(dbc.themes, DEFAULT_THEME_NAME, dbc.themes.DARKLY)

# Configurações de PDF
PDF_AUTHOR = "Simulador de Testes"
PDF_CREATOR = "Transformer Test Simulation Tool"

# -----------------------------------------------------------------------------
# Configurações de Logging
# -----------------------------------------------------------------------------
LOGGING_LEVEL = logging.DEBUG if DEBUG_MODE else logging.INFO
LOGGING_FORMAT = "%(asctime)s - %(name)s - %(levelname)s - %(message)s [%(filename)s:%(lineno)d]"
LOGGING_DATE_FORMAT = "%Y-%m-%d %H:%M:%S"

# -----------------------------------------------------------------------------
# Importação de Constantes Físicas
# -----------------------------------------------------------------------------
# Importar de utils/constants.py para centralizar as constantes físicas
from utils.constants import DEFAULT_FREQUENCY, EPSILON_0, MU_0, PI

# -----------------------------------------------------------------------------
# MIGRAÇÃO: Estilos removidos - usar apenas CSS
# -----------------------------------------------------------------------------
# Estilos migrados para pasta assets/ - usar apenas CSS
# from utils.styles import COLORS, COMPONENTS, MESSAGE_STYLES, SPACING, TABLE_STYLES, TYPOGRAPHY

# Cores temporárias para compatibilidade
COLORS = {
    "accent": "#26427A",
    "primary": "#26427A",
    "secondary": "#6c757d",
    "success": "#28a745",
    "info": "#17a2b8",
    "warning": "#ffc107",
    "danger": "#dc3545",
    "light": "#f8f9fa",
    "dark": "#343a40",
    "border": "#666666",
    "background_card": "#2c2c2c",
    "background_card_header": "#2a2a2a",
    "text_muted": "#6c757d",
    "text_light": "#f0f0f0",
    "background_main": "#1a1a1a",
}

COMPONENTS = {}
MESSAGE_STYLES = {}
SPACING = {}
TABLE_STYLES = {}
TYPOGRAPHY = {}

# Aliases para compatibilidade com código existente
colors = COLORS
TABLE_HEADER_STYLE_SM = {}
TABLE_HEADER_STYLE_MD = {}
TABLE_PARAM_STYLE_SM = {}
TABLE_PARAM_STYLE_MD = {}
TABLE_VALUE_STYLE_SM = {}
TABLE_VALUE_STYLE_MD = {}
TABLE_STATUS_STYLE = {}
TABLE_WRAPPER_STYLE = {}
PLACEHOLDER_STYLE = {}
ERROR_STYLE = {}
LABEL_STYLE = {}
INPUT_STYLE = {}
DROPDOWN_STYLE = {}
READ_ONLY_STYLE = {}
SECTION_TITLE_STYLE = {}
CARD_HEADER_STYLE = {}
