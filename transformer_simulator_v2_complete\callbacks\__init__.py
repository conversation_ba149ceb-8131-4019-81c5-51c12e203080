# callbacks/__init__.py
# Torna o diretório 'callbacks' um pacote Python.
# Definições de callbacks Dash, separadas por funcionalidade.

import logging

log = logging.getLogger(__name__)

# Importação de callbacks de integração de estilos
try:
    import callbacks.style_integration
    log.info("Callbacks de integração de estilos importados")
except ImportError as e:
    log.warning(f"Não foi possível importar callbacks de integração de estilos: {e}")
