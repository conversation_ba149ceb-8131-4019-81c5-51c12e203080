"""
Funções para garantir integridade dos dados.
"""
import copy
import logging
from typing import Any, Dict, List, Tuple

log = logging.getLogger(__name__)

# Constantes movidas para este arquivo para evitar dependências circulares
BASIC_FIELDS = [
    "potencia_mva", "tensao_at", "tensao_bt", "frequencia",
    "tipo_transformador", "conexao_at", "conexao_bt"
]

ALL_STORES = [
    "transformer-inputs-store",
    "losses-store", 
    "impulse-store",
    "dieletric-analysis-store",
    "applied-voltage-store",
    "induced-voltage-store",
    "short-circuit-store",
    "temperature-rise-store"
]

def validate_transformer_data(data: Dict[str, Any]) -> Tuple[bool, List[str]]:
    """
    Valida os dados básicos do transformador.

    Args:
        data: Dados do transformador a serem validados

    Returns:
        Tuple[bool, List[str]]: (v<PERSON>lid<PERSON>, lista de erros)
    """
    errors = []

    # Campos obrigatórios
    required_fields = [
        "potencia_mva", "tensao_at", "tensao_bt", "frequencia",
        "tipo_transformador", "conexao_at", "conexao_bt"
    ]

    # Verificar campos obrigatórios
    for field in required_fields:
        if field not in data or data[field] is None or data[field] == "":
            errors.append(f"Campo obrigatório '{field}' não preenchido")

    # Verificar valores numéricos
    numeric_fields = [
        "potencia_mva", "tensao_at", "tensao_bt", "tensao_terciario",
        "frequencia", "impedancia", "impedancia_tap_maior", "impedancia_tap_menor"
    ]

    for field in numeric_fields:
        if field in data and data[field] is not None and data[field] != "":
            try:
                float(data[field])
            except (ValueError, TypeError):
                errors.append(f"Campo '{field}' deve ser um valor numérico")

    # Verificar valores positivos
    positive_fields = [
        "potencia_mva", "tensao_at", "tensao_bt", "tensao_terciario",
        "frequencia", "impedancia"
    ]

    for field in positive_fields:
        if field in data and data[field] is not None and data[field] != "":
            try:
                value = float(data[field])
                if value <= 0:
                    errors.append(f"Campo '{field}' deve ser um valor positivo")
            except (ValueError, TypeError):
                pass  # Já tratado na verificação anterior

    # Verificar valores de enumeração
    if "tipo_transformador" in data and data["tipo_transformador"] not in ["Monofásico", "Trifásico"]:
        errors.append("Tipo de transformador deve ser 'Monofásico' ou 'Trifásico'")

    if "conexao_at" in data and data["conexao_at"] not in ["estrela", "triangulo"]:
        errors.append("Conexão AT deve ser 'estrela' ou 'triangulo'")

    if "conexao_bt" in data and data["conexao_bt"] not in ["estrela", "triangulo"]:
        errors.append("Conexão BT deve ser 'estrela' ou 'triangulo'")

    if "conexao_terciario" in data and data["conexao_terciario"] not in ["", "estrela", "triangulo"]:
        errors.append("Conexão terciário deve ser 'estrela', 'triangulo' ou vazio")

    # Verificar consistência entre campos
    if "tensao_terciario" in data and data["tensao_terciario"] and "conexao_terciario" not in data:
        errors.append("Conexão terciário deve ser especificada quando tensão terciário é informada")

    # Verificar consistência de impedância
    if "impedancia" in data and data["impedancia"] is not None and data["impedancia"] != "":
        try:
            impedancia = float(data["impedancia"])
            if impedancia < 0 or impedancia > 100:
                errors.append("Impedância deve estar entre 0 e 100%")
        except (ValueError, TypeError):
            pass  # Já tratado na verificação anterior

    return len(errors) == 0, errors

def ensure_data_consistency(store_id: str, data: dict[str, Any], transformer_inputs_data: dict[str, Any] | None = None) -> dict[str, Any]:
    """
    Garante a consistência dos dados de um store, usando apenas dados passados explicitamente.
    """
    consistent_data = copy.deepcopy(data)
    if store_id == "transformer-inputs-store":
        valid, errors = validate_transformer_data(consistent_data)
        if not valid:
            log.warning(f"[ensure_data_consistency] Dados inválidos: {errors}")
    elif transformer_inputs_data:
        if "transformer_data" not in consistent_data:
            consistent_data["transformer_data"] = {}
        # Sincroniza campos básicos
        for field in BASIC_FIELDS:
            if field in transformer_inputs_data and transformer_inputs_data[field] is not None:
                consistent_data["transformer_data"][field] = transformer_inputs_data[field]
    return consistent_data

def check_data_integrity(
    transformer_inputs_data: Dict[str, Any],
    all_store_data: Dict[str, Dict[str, Any]]
) -> Dict[str, List[str]]:
    """
    Verifica a integridade dos dados em todos os stores, usando apenas dados passados explicitamente.
    """
    errors = {}
    valid, transformer_errors = validate_transformer_data(transformer_inputs_data)
    if not valid:
        errors["transformer-inputs-store"] = transformer_errors
    # Verifica outros stores
    for store_id in ALL_STORES:
        if store_id == "transformer-inputs-store":
            continue
        store_data = all_store_data.get(store_id, {})
        if "transformer_data" not in store_data:
            errors.setdefault(store_id, []).append("transformer_data não encontrado")
            continue
        # Verifica consistência
        for field in BASIC_FIELDS:
            if transformer_inputs_data.get(field) != store_data["transformer_data"].get(field):
                errors.setdefault(store_id, []).append(f"Campo '{field}' inconsistente")
    return errors

def fix_data_integrity(app) -> Dict[str, bool]:
    """
    Corrige problemas de integridade dos dados.
    """
    if not hasattr(app, "state_manager") or not app.state_manager:
        return {"error": False}

    results = {"transformer-inputs-store": True}
    
    # Propaga dados para outros stores
    source_data = app.state_manager.get("transformer-inputs-store") or {}
    for store_id in ALL_STORES:
        if store_id == "transformer-inputs-store":
            continue
            
        store_data = app.state_manager.get(store_id) or {}
        if "transformer_data" not in store_data:
            store_data["transformer_data"] = {}
        
        # Sincroniza campos básicos
        for field in BASIC_FIELDS:
            if field in source_data and source_data[field] is not None:
                store_data["transformer_data"][field] = source_data[field]
        
        app.state_manager.set(store_id, store_data)
        results[store_id] = True

    return results
